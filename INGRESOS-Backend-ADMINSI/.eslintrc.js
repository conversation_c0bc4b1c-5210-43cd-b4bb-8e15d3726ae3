module.exports = {
  parser: '@babel/eslint-parser',
  env: {
    browser: true,
    es6: true,
  },
  extends: ['airbnb'],
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
    logger: 'readonly',
  },
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
  },
  rules: {
    camelcase: 'off',
    quotes: [2, 'single', { avoidEscape: true }],
    'no-console': 'off',
    'no-param-reassign': [2, { props: false }],
    'consistent-return': 'off',
    'no-underscore-dangle': 'off',
    'no-restricted-syntax': 'off',
    'no-continue': 'off',
    'no-await-in-loop': 'off',
    'no-shadow': 'off',
    'func-names': 'off',
    'import/prefer-default-export': 'off',
    'max-len': ['error', { code: 125 }],
  },
  settings: {
    'import/resolver': {
      alias: {
        map: [
          ['@functions', './src/database/functions'],
          ['@validators', './src/validators'],
          ['@postgresql', './src/database/pgConnection.js'],
          ['@sqlserver', './src/database/mssqlConnection.js'],
        ],
        extensions: ['.js', '.jsx', '.json'],
      },
    },
  },
};
