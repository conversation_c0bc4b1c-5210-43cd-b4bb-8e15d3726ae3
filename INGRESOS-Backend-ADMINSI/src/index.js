import Http from 'http';
import Express from 'express';
import 'regenerator-runtime';

import './config/env';
import './database';
import './utilities/logger';
import Router from './router';
import { DEV_ENV_SERVIDOR, DEV_PUERTO_SERVIDOR } from './constants/mensajes';

const port = process.env.PORT || 4001;
const app = Express();

Express.Router();
Router(app);
Http.createServer(app).listen(port, '0.0.0.0');

logger.log({ level: 'server', message: DEV_ENV_SERVIDOR });
logger.log({ level: 'server', message: DEV_PUERTO_SERVIDOR(port) });
