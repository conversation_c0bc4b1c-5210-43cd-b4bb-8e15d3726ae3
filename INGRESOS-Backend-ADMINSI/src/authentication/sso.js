import axios from 'axios';
import createHttpError from 'http-errors';

import { ERROR_SERVIDOR, SERVIDOR_NO_RESPONDE } from '../constants/mensajes';

const autenticacionCoppelAPI = axios.create({
  baseURL: process.env.SSO_API,
});

const endpoints = {
  me: '/v2/me',
  verify: '/v1/verify',
};

const headers = (token) => ({ headers: { Authorization: token } });

autenticacionCoppelAPI.interceptors.response.use(
  (response) => response.data.data,
  (error) => {
    if (error.response) {
      // Errores de respuesta del servidor
      const { data, status } = error.response;
      const mensajeRespuesta = data?.data ? data.data.menssage : ERROR_SERVIDOR;
      const codigoError = data?.data ? data.data.errorCode : status;

      return Promise.reject(createHttpError(codigoError, mensajeRespuesta));
    }

    if (error.request) {
      // Errores de solicitud, cuando no hay respuesta recibida
      return Promise.reject(createHttpError(503, SERVIDOR_NO_RESPONDE(autenticacionCoppelAPI.defaults.baseURL)));
    }

    // Errores generales o de configuración
    return Promise.reject(createHttpError(500, error.message));
  },
);

/**
 * Verifica un token de autenticación.
 * @param {string} token - Token de autenticación.
 * @returns {Promise} - Promesa que resuelve con la respuesta del servidor.
 */
export async function verificarToken(token) {
  const respuestaVerificador = await autenticacionCoppelAPI.post(endpoints.verify, {}, headers(token));
  return respuestaVerificador;
}

/**
 * Obtiene información del usuario utilizando un token de autenticación.
 * @param {string} token - Token de autenticación.
 * @returns {Promise} - Promesa que resuelve con la información del usuario.
 */
export async function meToken(token) {
  const informacionUsuarioIdp = await autenticacionCoppelAPI.get(endpoints.me, headers(token));
  return informacionUsuarioIdp;
}
