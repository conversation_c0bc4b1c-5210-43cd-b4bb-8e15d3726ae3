import app from 'express';
import createHttpError from 'http-errors';

import { ERROR_AUTENTICACION } from '../constants';

const router = app.Router();

/**
 * Método para obtener los datos del empleado autenticado
 */
router.get('/me', async (req, res, next) => {
  try {
    const { usuarioIdp, usuarioMonitor } = req.session || {};
    const method = req.headers.idp;

    if (!usuarioIdp || !usuarioMonitor) throw createHttpError(401, ERROR_AUTENTICACION);

    return res.status(200).json({ ...usuarioMonitor, ...usuarioIdp, idP: method });
  } catch (error) {
    next(error);
  }
});

export default router;
