import app from 'express';
import createHttpError from 'http-errors';

import {
  eliminarUsuario,
  obtenerListaUsuarios,
  obtenerUsuario,
  registrarActualizarUsuario,
} from '@functions/usuarios';

import { esquemaPost, esquemaPut } from '@validators/usuarios';
import { esquemaListado, esquemaPorId } from '@validators/common';

import {
  loguearTransaccion,
  validarBody,
  validarCampos,
  validarParams,
  validarQuery,
} from '../middlewares';

import {
  ERROR_ELIMINAR,
  ERROR_REGISTRO,
  RECURSO_NO_ENCONTRADO,
  NUM_REGISTROS_PAGINADOR,
} from '../constants';

const router = app.Router();

/**
 * Método para obtener los usuarios
 */
router.get('/lista', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      busqueda = '',
      registrosPorPagina = NUM_REGISTROS_PAGINADOR,
      pagina = 1,
    } = req.query;

    const { rows: [{ lista: { total, registros } }] } = await obtenerListaUsuarios({
      busqueda,
      registrosPorPagina,
      pagina,
    });

    return res.status(200).json({ total, registros: registros || [] });
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener un usuario por Id
 */
router.get('/:idu', validarParams(esquemaPorId), async (req, res, next) => {
  try {
    const { idu } = req.params;

    const { rows: [{ usuario }] } = await obtenerUsuario(Number(idu));
    if (!usuario) return res.status(404).json(RECURSO_NO_ENCONTRADO);

    return res.status(200).json(usuario);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para crear un usuario
 */
router.post('/', validarBody(esquemaPost), loguearTransaccion, async (req, res, next) => {
  try {
    const { rows: [{ resultado }] } = await registrarActualizarUsuario(req.body);

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar un usuario
 */
router.put('/:idu', validarCampos(esquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { idu_si_rol, opc_activo } = req.body;

    const { rows: [{ resultado }] } = await registrarActualizarUsuario({
      idu_si_usuario: Number(idu),
      idu_si_rol,
      opc_activo,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para eliminar un usuario
 */
router.delete('/:idu', validarParams(esquemaPorId), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { rows: [{ resultado }] } = await eliminarUsuario(Number(idu));

    if (!resultado) throw createHttpError(400, ERROR_ELIMINAR);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
