import app from 'express';
import createHttpError from 'http-errors';

import {
  eliminarPerfil, obtenerListaPerfiles, obtenerPerfil, obtenerPerfiles, registrarActualizarPerfil,
} from '@functions/perfiles';
import { esquemaPost, esquemaPut } from '@validators/perfiles';
import { esquemaListado, esquemaPorId } from '@validators/common';

import {
  validarBody, validarCampos, validarParams, validarQuery, loguearTransaccion,
} from '../middlewares';
import {
  ERROR_ACTUALIZAR,
  ERROR_ELIMINAR,
  ERROR_REGISTRO,
  RECURSO_NO_ENCONTRADO,
  NUM_REGISTROS_PAGINADOR,
} from '../constants';

const router = app.Router();

/**
 * Método para obtener los perfiles
 */
router.get('/', async (req, res, next) => {
  try {
    const { rows: [{ lista: { registros } }] } = await obtenerPerfiles();

    return res.status(200).json(registros);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener los perfiles con paginación
 */
router.get('/lista', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      busqueda = '',
      registrosPorPagina = NUM_REGISTROS_PAGINADOR,
      pagina = 1,
    } = req.query;

    const { rows: [{ lista }] } = await obtenerListaPerfiles({
      busqueda,
      registrosPorPagina,
      pagina,
    });

    return res.status(200).json(lista);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener un perfil por Id
 */
router.get('/:idu', validarParams(esquemaPorId), async (req, res, next) => {
  try {
    const { idu } = req.params;

    const { rows: [{ perfil }] } = await obtenerPerfil(Number(idu));
    if (!perfil) return res.status(404).json(RECURSO_NO_ENCONTRADO);

    return res.status(200).json(perfil);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para crear un perfil
 */
router.post('/', validarBody(esquemaPost), loguearTransaccion, async (req, res, next) => {
  try {
    const { rows: [{ resultado }] } = await registrarActualizarPerfil(req.body);

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_REGISTRO);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para actualizar un perfil
 */
router.put('/:idu', validarCampos(esquemaPut), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;

    const { rows: [{ resultado }] } = await registrarActualizarPerfil({
      idu_si_rol: Number(idu),
      ...req.body,
    });

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_ACTUALIZAR);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

/**
 * Método para eliminar un perfil
 */
router.delete('/:idu', validarParams(esquemaPorId), loguearTransaccion, async (req, res, next) => {
  try {
    const { idu } = req.params;
    const { rows: [{ resultado }] } = await eliminarPerfil(Number(idu));

    if (!resultado.estatus) throw createHttpError(400, resultado.mensaje || ERROR_ELIMINAR);

    return res.status(204).json();
  } catch (error) {
    next(error);
  }
});

export default router;
