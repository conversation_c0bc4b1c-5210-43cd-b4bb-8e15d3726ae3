import app from 'express';

import { esquemaListado } from '@validators/common';
import {
  obtenerModulos,
  obtenerUsuarios,
} from '@functions/catalogos';
import { validarQuery } from '../middlewares';
import { construirArbolModulos } from '../utilities/funciones';
import {
  NUM_REGISTROS_PAGINADOR,
} from '../constants';

const router = app.Router();

/**
 * Método para obtener el catálogo de módulos en forma de arbol (menú)
 */
router.get('/modulos', async (req, res, next) => {
  try {
    const { rows: [{ modulos }] } = await obtenerModulos();
    const arbol = construirArbolModulos(modulos);
    return res.status(200).json(arbol);
  } catch (error) {
    next(error);
  }
});

/**
 * Método para obtener el catálogo de usuarios
 */
router.get('/empleados', validarQuery(esquemaListado), async (req, res, next) => {
  try {
    const {
      busqueda = '',
      registrosPorPagina = NUM_REGISTROS_PAGINADOR,
      pagina = 1,
    } = req.query;

    const { output } = await obtenerUsuarios({ busqueda, registrosPorPagina, pagina });

    return res.status(200).json({
      total: output.total,
      registros: JSON.parse(output.registros) || [],
    });
  } catch (error) {
    next(error);
  }
});

export default router;
