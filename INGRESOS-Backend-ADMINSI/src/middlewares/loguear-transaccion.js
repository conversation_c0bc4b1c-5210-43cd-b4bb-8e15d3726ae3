import { registrarLogTransaccion } from '@functions/logs';
import { ROL_ADMIN } from '../constants';

const obtenerRecurso = (url) => url.split('/').pop();

const obtenerTipoTransaccion = (method) => {
  switch (method) {
    case 'POST':
      return 'Insercion';
    case 'PUT':
      return 'Actualizacion';
    case 'DELETE':
      return 'Eliminacion';
    default:
      return '';
  }
};

/**
 * @type {import('express').Handler}
 */
export const loguearTransaccion = (req, res, next) => {
  const { baseUrl, method, ip } = req;

  const recurso = obtenerRecurso(baseUrl);
  const tipoTransaccion = obtenerTipoTransaccion(method);

  res.on('finish', async () => {
    const { statusCode } = res;
    const { usuarioMonitor } = req.session;
    const { num_empleado } = usuarioMonitor;

    const estatus = statusCode >= 200 && statusCode < 300;

    registrarLogTransaccion({
      estatus,
      ip,
      num_empleado,
      recurso,
      rol: ROL_ADMIN,
      tipo_transaccion: tipoTransaccion,
    });
  });

  next();
};
