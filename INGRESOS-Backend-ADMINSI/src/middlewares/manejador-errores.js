import { ERROR_SERVIDOR } from '../constants/mensajes';
import { log } from '../utilities/logger';

/**
 * Manejador de errores de la aplicación
 * @param {import('express').ErrorRequestHandler} err Objeto de error de Express
 * @param {import('express').Request} req Objeto de solicitud de Express
 * @param {import('express').Response} res Objeto de respuesta de Express
 * @param {import('express').NextFunction} next Función de siguiente middleware
 */
export const manejadorErrores = (error, req, res, next) => {
  let errorHandler = error;
  const { method, path } = req;

  // Validar si err es una instancia de Error
  if (!(errorHandler instanceof Error)) {
    // Si no es una instancia de Error, crear un nuevo error con un mensaje genérico
    errorHandler = new Error(ERROR_SERVIDOR);
  }

  const { name, message, statusCode = 500 } = errorHandler;
  const mensajeError = statusCode >= 500 ? ERROR_SERVIDOR : message;

  // Agregar log de errores con información adicional
  log({
    isError: true,
    title: `[${method}] - ${path} - ${statusCode}`,
    descripcion: `${name}: ${message}`,
    objeto: { ...errorHandler, clientIP: req.ip, userAgent: req.get('user-agent') },
  });

  // Registrar el error en la consola solo en desarrollo
  if (process.env.NODE_ENV === 'development') {
    logger.error(errorHandler);
  }

  res.status(statusCode).json(mensajeError);
  next();
};

export default manejadorErrores;
