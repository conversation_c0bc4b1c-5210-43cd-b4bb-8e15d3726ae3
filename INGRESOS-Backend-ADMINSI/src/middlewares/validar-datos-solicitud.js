import chalk from 'chalk';

/**
 * Middleware de validación de Joi para el cuerpo de la solicitud.
 *
 * @param {import('joi').Schema} esquema Esquema de Joi
 * @param {string} [propiedad='body'] Propiedad de la solicitud a validar
 * @returns {import('express').RequestHandler} Middleware de validación de Joi
 *
 * @example
 * import app from 'express';
 * import Joi from 'joi';
 *
 * import validarBody from './middlewares/validarDatosSolicitud';
 *
 * const router = app.Router();
 *
 * const esquema = Joi.object({
 *  prop1: Joi.string().required(),
 *  prop2: Joi.number().required(),
 *  prop3: Joi.boolean().required(),
 * });
 *
 * router.post('/', validarBody(esquema), (req, res) => {
 *  // ...
 * });
 */
function validarDatosSolicitud(esquema, propiedad = 'body') {
  return (req, res, next) => {
    const { error } = esquema.validate(req[propiedad]);
    if (error) {
      if (process.env.NODE_ENV === 'development') {
        logger.debug(chalk.bold('Validación:'), error.details[0].message);
      }
      return res.status(400).json(error.details[0].message);
    }

    next();
  };
}

/**
 * Middleware de validación de Joi para los parámetros, query params y body de la solicitud.
 *
 * @param {Object} esquemas Esquemas de Joi para validar los datos de la solicitud
 * @param {import('joi').Schema} [esquemas.params] Esquema para validar los parámetros de la solicitud
 * @param {import('joi').Schema} [esquemas.query] Esquema para validar los query params de la solicitud
 * @param {import('joi').Schema} [esquemas.body] Esquema para validar el body de la solicitud
 * @returns {import('express').RequestHandler} Middleware de validación de Joi
 */
export function validarCampos(esquemas = {}) {
  return (req, res, next) => {
    try {
      const { params, query, body } = esquemas;

      const { error: paramsError } = params?.validate(req.params) || {};
      const { error: queryError } = query?.validate(req.query) || {};
      const { error: bodyError } = body?.validate(req.body) || {};

      const manejarValidacionError = (error) => {
        if (process.env.NODE_ENV === 'development') {
          logger.debug(chalk.bold('Validación:'), error.details[0].message);
        }
        res.status(400).json(error.details[0].message);
      };

      if (paramsError) return manejarValidacionError(paramsError);
      if (queryError) return manejarValidacionError(queryError);
      if (bodyError) return manejarValidacionError(bodyError);

      next();
    } catch (error) {
      next(error);
    }
  };
}

export const validarBody = (esquema) => validarDatosSolicitud(esquema, 'body');
export const validarQuery = (esquema) => validarDatosSolicitud(esquema, 'query');
export const validarParams = (esquema) => validarDatosSolicitud(esquema, 'params');

export default validarDatosSolicitud;
