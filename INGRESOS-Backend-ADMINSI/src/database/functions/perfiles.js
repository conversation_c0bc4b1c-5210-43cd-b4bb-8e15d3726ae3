import * as db from '@postgresql';

import {
  NUM_REGISTROS_PAGINADOR,
} from '../../constants';

/**
 * Función para obtener los perfiles
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  lista: {
 *    total: Number,
 *    registros: Array<{
 *       idu_perfil: Number,
 *       nombre: String
 *       descripcion: String
 *       estatus: Boolean
 *       modulos: Array<{ idu_modulo: Number }>
 *    }>
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerPerfiles() {
  const parametros = [''];
  const query = 'SELECT fun_obtener_roles_sistema_web_ingresos($1) as lista;';
  return db.query(query, parametros);
}

/**
 * Función para obtener la lista de perfiles
 * @param {String} [parametros.busqueda] Término a buscar
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  lista: {
 *    total: Number,
 *    registros: Array<{
 *       idu_perfil: Number,
 *       nombre: String
 *       descripcion: String
 *       estatus: Boolean
 *       modulos: Array<{ idu_modulo: Number }>
 *    }>
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerListaPerfiles({
  busqueda = '',
  registrosPorPagina = NUM_REGISTROS_PAGINADOR,
  pagina = 1,
}) {
  const parametros = [
    busqueda,
    Number(registrosPorPagina),
    Number(pagina - 1 || 0) * Number(registrosPorPagina),
  ];

  const query = 'SELECT public.fun_listar_roles_sistema_web_ingresos($1, $2, $3) AS lista;';

  return db.query(query, parametros);
}

/**
 * Función para obtener un perfil por Id
 *
 * @param {Number} idu_si_rol Identificador del perfil a obtener
 * @returns {Promise<import('pg').QueryResult<{
 *  perfil: {
 *    idu_perfil: Number,
 *    nombre: String
 *    descripcion: String
 *    estatus: Boolean
 *    modulos: Array<{ idu_modulo: Number }>
 *  }
 * }>>} Promesa
 * con el resultado de la consulta
 */
export function obtenerPerfil(idu_si_rol) {
  const query = 'SELECT fun_obtener_rol_sistema_web_ingresos($1) AS perfil;';
  return db.query(query, [idu_si_rol]);
}

/**
 * Función para insertar o actualizar un perfil
 *
 * @param {Object} parametros Parametros de la función
 * @param {String} parametros.nom_rol Nombre del perfil
 * @param {String} parametros.des_rol Descripción del perfil
 * @param {Boolean} parametros.opc_activo Estatus del perfil
 * @param {Object} [parametros.modulos] Identificadores de los módulos
 * @param {Number} [parametros.idu_si_rol] Identificador del perfil a actualizar
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: { estatus: Boolean mensaje?: String } }>
 * } Promesa con el resultado de la consulta
 */
export function registrarActualizarPerfil({
  idu_si_rol = null,
  nom_rol = null,
  des_rol = null,
  opc_activo = null,
  modulos = null,
}) {
  const parametros = [
    idu_si_rol,
    nom_rol,
    des_rol,
    opc_activo,
    JSON.stringify(modulos),
  ];

  const query = 'SELECT fun_upsert_roles_sistema_web_ingresos($1, $2, $3, $4, $5) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para eliminar un perfil
 *
 * @param {Number} iduPerfil Identificador del perfil a eliminar
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function eliminarPerfil(iduPerfil) {
  const query = 'SELECT fun_eliminar_rol_sistema_web_ingresos($1) as resultado';
  return db.query(query, [iduPerfil]);
}
