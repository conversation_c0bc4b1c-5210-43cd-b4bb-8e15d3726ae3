import * as db from '@postgresql';

/**
 * Verifica si un empleado tiene acceso al sistema
 *
 * @param {number} numEmpleado Número de empleado
 * @returns {Promise<import('pg').QueryResult<{ resultado: boolean | null }>} Promesa
 *  con el resultado de la consulta
 */
export function validarAutenticacionUsuario(numEmpleado) {
  const query = 'SELECT fun_autenticar_gestor_sistema_web_ingresos($1) as resultado';
  return db.query(query, [numEmpleado]);
}
