import * as db from '@postgresql';

/**
 * @param {{
 *  ip: string;
 *  estatus: boolean;
 *  tipo_transaccion: 'Insercion' | 'Actualizacion' | 'Eliminacion';
 *  recurso: string;
 *  num_empleado: number;
 *  rol: string;
 * }} log
 */
export function registrarLogTransaccion(log) {
  const {
    num_empleado, estatus, ip, recurso, tipo_transaccion, rol,
  } = log;
  const parametros = [num_empleado, estatus, recurso, tipo_transaccion, ip, rol];
  const query = 'SELECT fun_registrar_log_transaccion_sistema_web_ingresos($1, $2, $3, $4, $5, $6) as resultado';
  return db.query(query, parametros);
}

/**
 * @param {{
 *  ip: string;
 *  estatus: boolean;
 *  transaccion_realizada: string;
 * }} log
 */
export function registrarLogSeguridad(log) {
  const { estatus, ip, transaccion_realizada } = log;
  const parametros = [transaccion_realizada, ip, estatus];
  const query = 'SELECT fun_registrar_log_seguridad_sistema_web_ingresos($1, $2, $3) as resultado';
  return db.query(query, parametros);
}
