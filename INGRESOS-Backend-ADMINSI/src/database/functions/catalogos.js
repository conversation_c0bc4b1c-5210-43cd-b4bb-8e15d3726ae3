import sql from 'mssql';

import * as dbPg from '@postgresql';
import * as dbMssql from '@sqlserver';

import {
  NUM_REGISTROS_PAGINADOR,
} from '../../constants';

/**
 * Función para obtener el catálogo de estatus
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  idu_estatus_conciliacion: Number,
 *  nombre: String,
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerCatalogoEstatus() {
  const query = 'SELECT idu_estatus_conciliacion, nombre FROM fun_obtenercatalogoestatus();';
  return dbPg.query(query, []);
}

/**
 * Función para obtener los perfiles
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  modulos: {
 *    idu_modulo: Number
 *    nombre: String
 *    descripcion: String
 *    idu_modulo_padre: Number
 *    estatus: Boolean,
 *    procesos: [{
 *      idu_conciliacion: number;
 *      nom_conciliacion: string;
 *    }]
 *  }[]
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerModulos() {
  const parametros = [];
  const query = 'SELECT fun_obtener_modulos_sistema_web_ingresos() as modulos;';
  return dbPg.query(query, parametros);
}

/**
 * Función para obtener los usuarios
 *
 * @returns {Promise<import('mssql').IResult<{
 *  idu_empleado: Number,
 *  nombre: String,
 *  puesto: String,
 *  telefono: String,
 * }}>
 */
export function obtenerUsuarios({ busqueda = '', registrosPorPagina = NUM_REGISTROS_PAGINADOR, pagina = 1 }) {
  const procedimientoAlmacenado = 'proc_obtenerempleadosmonitores';
  const parametros = {
    pBusqueda: busqueda,
    pLimit: Number(registrosPorPagina),
    pOffset: Number(pagina - 1 || 0) * Number(registrosPorPagina),
  };

  const salida = {
    total: sql.Int,
    registros: sql.NVarChar,
  };

  return dbMssql.execute(procedimientoAlmacenado, parametros, salida);
}

/**
 * Función que obtiene todas las palabras clave
 * @returns {
 *  Promise<{
 *    idu_conciliacion_reemplazo_cadena: number;
 *    des_remplazo:                      string;
 *    des_cadena_reemplazar:             string;
 *    des_cadena_reemplazo:              string;
 *  }[]>
 * }
 */
export function obtenerPalabrasClave() {
  const query = 'SELECT fun_obtenerpalabrasclave() as resultado';
  return dbPg.query(query, []);
}
