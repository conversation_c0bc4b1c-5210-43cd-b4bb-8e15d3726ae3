import * as db from '@postgresql';

import {
  NUM_REGISTROS_PAGINADOR,
} from '../../constants';

/**
 * Función para obtener la lista de usuarios
 * @param {String} [parametros.busqueda] Término a buscar
 * @param {Number} [parametros.registrosPorPagina] Registros por página
 * @param {Number} [parametros.pagina] Página
 *
 * @returns {Promise<import('pg').QueryResult<{
 *  lista: {
 *    total: Number,
 *    registros: {
*       idu_usuario: Number
 *      num_empleado: Number
 *      nom_empleado: String
 *      perfil: { idu_perfil: Number, nombre: String }
 *      nom_puesto: String
 *      num_telefono: String
 *      opc_estatus: Boolean
 *    }[]
 * }>>} Promesa con el resultado de la consulta
 */
export function obtenerListaUsuarios({
  busqueda = '',
  registrosPorPagina = NUM_REGISTROS_PAGINADOR,
  pagina = 1,
}) {
  const parametros = [
    busqueda,
    Number(registrosPorPagina),
    Number(pagina - 1 || 0) * Number(registrosPorPagina),
  ];

  const query = 'SELECT fun_obtener_usuarios_sistema_web_ingresos($1, $2, $3) AS lista;';

  return db.query(query, parametros);
}

/**
 * Función para obtener un usuario por Id
 *
 * @param {Number} idu_si_usuario Identificador del usuario a obtener
 * @returns {Promise<import('pg').QueryResult<{
*  usuario: {
  *    idu_si_usuario: Number
  *    num_empleado: String
  *    idu_si_rol: Number
  *    nom_rol: String
  *    nom_empleado: String
  *    nom_puesto: String
  *    num_telefono: String
  *    opc_estatus: Boolean
  *    modulos: Array<{
  *               idu_modulo: Number
  *               nom_modulo: String
  *               opc_solo_lectura: Boolean
  *             }>
  *  }
  * }>>} Promesa
  * con el resultado de la consulta
  */
export function obtenerUsuario(idu_si_usuario) {
  const query = 'SELECT fun_obtener_usuario_sistema_web_ingresos($1) AS usuario;';
  return db.query(query, [idu_si_usuario]);
}

/**
 * Función para insertar o actualizar un usuario
 *
 * @param {Object} parametros Parametros de la función
 * @param {Number} [parametros.idu_si_usuario] Identificador del usuario a actualizar
 * @param {Number} parametros.idu_si_perfil Identificador del perfil
 * @param {Number} parametros.num_empleado Número de empleado
 * @param {String} parametros.nom_usuario Nombre del empleado
 * @param {String} parametros.nom_puesto Nombre del puesto
 * @param {String} parametros.num_telefono Número de teléfono
 * @param {Boolean} [parametros.opc_estatus] Estatus del usuario
 * @returns {Promise<import('pg').QueryResult<{
 *  resultado: { estatus: Boolean mensaje?: String } }>
 * } Promesa con el resultado de la consulta
 */
export function registrarActualizarUsuario({
  idu_si_usuario = null,
  idu_si_rol = null,
  num_empleado = null,
  nom_usuario = null,
  nom_puesto = null,
  num_telefono = null,
  opc_activo = true,
}) {
  const parametros = [
    idu_si_usuario,
    idu_si_rol,
    num_empleado,
    nom_usuario,
    nom_puesto,
    num_telefono,
    opc_activo,
  ];

  const query = 'SELECT fun_upsert_usuarios_sistema_web_ingresos($1, $2, $3, $4, $5, $6, $7) as resultado';
  return db.query(query, parametros);
}

/**
 * Función para eliminar un usuario
 *
 * @param {Number} iduUsuario Identificador del usuario a eliminar
 * @returns {Promise<import('pg').QueryResult<{ resultado: Boolean }>} Promesa
 *  con el resultado de la consulta
 */
export function eliminarUsuario(iduUsuario) {
  const query = 'SELECT fun_eliminar_usuario_sistema_web_ingresos($1) as resultado';
  return db.query(query, [iduUsuario]);
}
