CREATE TABLE his_si_registro_transacciones (
    idu UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    num_empleado VARCHAR(8) NOT NULL,
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE :: TIM<PERSON><PERSON><PERSON>,
    des_estatus VARCHAR(10) CHECK (des_estatus IN ('Exito', 'Fallo')),
    nom_recurso VARCHAR(100) NOT NULL,
    nom_rol VARCHAR(50) NOT NULL,
    nom_tipo_transaccion VARCHAR(20) NOT NULL,
    clv_ip INET NOT NULL
);

CREATE TABLE his_si_registro_seguridad (
    idu UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE :: TIMESTAMP,
    des_estatus VARCHAR(10) CHECK (des_estatus IN ('Exito', 'Fallo')),
    des_transaccion_realizada VARCHAR(250),
    clv_ip INET NOT NULL
);