CREATE OR REPLACE FUNCTION fun_registrar_log_seguridad_sistema_web_ingresos(
    p_des_transaccion_realizada VARCHAR(250),
    p_clv_ip INET,
    p_des_estatus BOOLEAN
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 01/04/2025
-- Descripcion General: Funcion que inserta un registro en la tabla de his_si_registros_seguridad.
-- ========================================================================

RETURNS JSON AS

$BODY$
DECLARE
    registro_insertado INT := 0;
BEGIN
    INSERT INTO his_si_registro_seguridad (
        des_transaccion_realizada,
        clv_ip,
        des_estatus
    ) VALUES (
        p_des_transaccion_realizada,
        p_clv_ip,
        CASE WHEN p_des_estatus THEN 'Exito' ELSE 'Fallo' END
    );

	-- Si la insercion se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', 'Registro insertado correctamente.');
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', 'Error al insertar el registro.');
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', SQLERRM);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_registrar_log_seguridad_sistema_web_ingresos(
    p_des_transaccion_realizada VARCHAR(250),
    p_clv_ip INET,
    p_des_estatus BOOLEAN
) IS 'Funcion que inserta un registro en la tabla de his_si_registros_seguridad.
p_des_transaccion_realizada = Descripcion de la accion realizada
p_clv_ip = Ip del origen
p_des_estatus = Estatus del resultado de la transaccion
';
