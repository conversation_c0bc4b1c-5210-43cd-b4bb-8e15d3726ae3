CREATE TABLE cat_si_modulos (
    idu_si_modulo SERIAL PRIMARY KEY,
    idu_si_modulo_padre INT NOT NULL DEFAULT 0,
    clv_modulo_si INT NOT NULL DEFAULT 0,
    nom_modulo TEXT NOT NULL DEFAULT '',
    opc_modulo_web BIT(1) NOT NULL DEFAULT B'0',
    opc_modulo_obligatorio BIT(1) NOT NULL DEFAULT B'0',
    des_ruta TEXT NOT NULL DEFAULT '',
    des_icono TEXT NOT NULL DEFAULT '',
    num_orden INT NOT NULL DEFAULT 1,
    opc_estatus BIT(1) NOT NULL DEFAULT B'1',
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    FOREIGN KEY (idu_si_modulo_padre) REFERENCES cat_si_modulos (idu_si_modulo)
);

CREATE TABLE cat_si_roles (
    idu_si_rol SERIAL PRIMARY KEY,
    nom_rol TEXT NOT NULL DEFAULT '',
    des_rol TEXT NOT NULL DEFAULT '',
    opc_activo BIT(1) NOT NULL DEFAULT B'1',
    opc_estatus BIT(1) NOT NULL DEFAULT B'1',
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP
);

CREATE TABLE cat_si_usuarios (
    idu_si_usuario SERIAL PRIMARY KEY,
    clv_numemp CHARACTER(8) NOT NULL DEFAULT '',
    idu_si_rol INT NOT NULL DEFAULT 0,
    nom_usuario TEXT NOT NULL DEFAULT '',
    nom_puesto TEXT NOT NULL DEFAULT '',
    num_telefono VARCHAR(10) NOT NULL DEFAULT '',
    opc_activo BIT(1) NOT NULL DEFAULT B'1',
    opc_estatus BIT(1) NOT NULL DEFAULT B'1',
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    FOREIGN KEY (idu_si_rol) REFERENCES cat_si_roles (idu_si_rol)
);

CREATE TABLE cat_gestor_si_usuarios (
    idu_si_usuario_gestor SERIAL PRIMARY KEY,
    clv_numemp CHARACTER(8) NOT NULL DEFAULT '',
    nom_usuario TEXT NOT NULL DEFAULT '',
    opc_estatus BIT(1) NOT NULL DEFAULT B'1',
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP
);

CREATE TABLE cat_si_rol_permisos (
    idu_si_rol_permisos SERIAL PRIMARY KEY,
    idu_si_rol INT NOT NULL DEFAULT 0,
    idu_si_modulo INT NOT NULL DEFAULT 0,
    opc_solo_lectura BIT(1) NOT NULL DEFAULT B'1',
    opc_estatus BIT(1) NOT NULL DEFAULT B'1',
    fec_registro TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    fec_actualizacion TIMESTAMP NOT NULL DEFAULT CURRENT_DATE::TIMESTAMP,
    FOREIGN KEY (idu_si_rol) REFERENCES cat_si_roles (idu_si_rol),
    FOREIGN KEY (idu_si_modulo) REFERENCES cat_si_modulos (idu_si_modulo),
    UNIQUE(idu_si_rol, idu_si_modulo)
);

-- Importante la eliminacion de 'UNIQUE(clv_modulo_si)' de la tabla cat_si_modulos
-- Elemento Raiz
INSERT INTO cat_si_modulos (idu_si_modulo, idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES (0, 0, 0, 'Raiz');

-- Primer opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 0, 'Proceso Diario');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32771, 'Bajar Archivos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32830, 'Reporte de Cobranza Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32775, 'Ajustes Cortes de Caja');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32805, 'Consultas Cortes de Cajas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 33015, 'Consulta a Detalle de movimientos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Archivo de Cheques');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Archivo de Cheques'), 32776, 'Bajar Archivos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Archivo de Cheques'), 32816, 'Reporte de Cheques por Dia');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32810, 'Marcar Archivos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32809, 'Actualizar Hojas de Corte');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32821, 'Cortes Actualizados');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Generar Polizas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Generar Polizas'), 32777, 'P&oacute;liza Diaria Ingresos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Generar Polizas'), 32979, 'Desmarcar Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Generar Polizas'), 32980, 'Faltante Sobrante Banco');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Generar Polizas'), 33006, 'Asignaci&oacute;n de subcuentas por movimientos y secci&oacute;n de centros');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Generar Polizas'), 33007, 'Correcci&oacute;n de Centros de Movimientos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32790, 'Reporte Tarjetas Bancarias por Dia');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Reporte Tarjetas Bancarias Por Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte Tarjetas Bancarias Por Tienda'), 32954, 'Bloque');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte Tarjetas Bancarias Por Tienda'), 32955, 'Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria'), 32972, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria'), 32973, 'Consulta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria'), 0, 'Desconcilia');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Desconcilia'), 32974, 'Importe');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Desconcilia'), 32975, 'Tienda - Fecha');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria'), 32977, 'Consultar Dep&oacute;sitos ');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria Depositos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Depositos'), 32933, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Depositos'), 32934, 'Consulta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Depositos'), 32850, 'Reporte Diferencia Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Depositos'), 32899, 'Reporte de Dep&oacute;sitos Pendientes');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria Tarjetas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 32844, 'Proceso de Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 33013, 'Consulta Conciliaci&oacute;n Tarjetas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 0, 'Desconciliar'); -- No se encontro clv_modulo_si
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 32848, 'Reporte Diferencias Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 32847, 'Reporte Conciliaci&oacute;n Tarjeta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 32849, 'Captura Dep&oacute;sitos Tarjeta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Tarjetas'), 32877, 'Captura de Cancelaciones Bancomer');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria Bital');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Bital'), 32931, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Bital'), 32932, 'Consulta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Bital'), 32937, 'Reporte Diferencias Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria HSBC Internet');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 32856, 'Proceso de Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 32861, 'Consulta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 0, 'Desconciliar'); -- No se encontro clv_modulo_si
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 32869, 'Reporte Diferencia Conciliaci&oacute;n ');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 33003, 'Cargar Dep&oacute;sitos HSBC');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 33004, 'Cargar Dep&oacute;sitos Facturados Internet');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria HSBC Internet'), 33005, 'Consulta Dep&oacute;sitos Internet Facturados');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria Bancrecer');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Bancrecer'), 32865, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Bancrecer'), 32866, 'Consulta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Bancrecer'), 32868, 'Reporte Diferencias Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria Santander');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Santander'), 32882, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Santander'), 32883, 'Consultas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Santander'), 32885, 'Reporte Diferencias Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Diaria Banamex');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Banamex'), 32947, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Banamex'), 32948, 'Consultas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Diaria Banamex'), 32950, 'Reporte Diferencia Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Bancoppel');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Bancoppel'), 32962, 'Proceso Conciliaci&oacute;n Diaria');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Bancoppel'), 32965, 'Consulta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Bancoppel'), 32964, 'Reporte Diferencia Conciliaci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 32841, 'Impresi&oacute;n Cortes Tiendas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Comentarios Diferencias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Comentarios Diferencias'), 32872, 'Captura Comentarios');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Comentarios Diferencias'), 32886, 'Consulta Comentarios Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Comentarios Diferencias'), 32874, 'Reporte x Fecha y Bloque');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Comentarios Diferencias'), 32875, 'Reporte Mensual Diferencia > 500');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Faltantes y Sobrantes Banco');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Faltantes y Sobrantes Banco'), 32863, 'Faltantes y Sobrantes Reportados por el Banco (P&oacute;liza)');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Faltantes y Sobrantes Banco'), 0, 'Cargar Archivo Faltante Sobrante');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Cargar Archivo Faltante Sobrante'), 32942, 'Moneda Nacional');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Cargar Archivo Faltante Sobrante'), 32943, 'D&oacute;lares');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Faltantes y Sobrantes Banco'), 0, 'Captura Faltante Sobrante Para Envio Correo');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Captura Faltante Sobrante Para Envio Correo'), 32944, 'Moneda Nacional');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Captura Faltante Sobrante Para Envio Correo'), 32945, 'D&oacute;lares');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Faltantes y Sobrantes Banco'), 32941, 'Envio Correo Faltante Sobrante Banco');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Reporte de Diferencias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte de Diferencias'), 32952, 'Capturar Diferencias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte de Diferencias'), 32953, 'consultar Diferencias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Diario'), 0, 'Conciliaci&oacute;n Corresponsales');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Corresponsales'), 32996, 'Proceso Diario');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Corresponsales'), 32997, 'Conciliar');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaci&oacute;n Corresponsales'), 32998, 'Desconciliar');

-- Segunda opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 0, 'Proceso Mensual');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Mensual'), 0, 'Reportes');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32989, 'Dolares Mensual por Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 0, 'Dep&oacute;sitos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Dep&oacute;sitos'), 32784, 'Estado de Cuenta');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Dep&oacute;sitos'), 32785, 'Estado de Cuenta U.S.A');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32787, 'Dolares Mensual por dia.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32858, 'Dolares Mensual Gastos Viaje');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32860, 'Resumen Tiendas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Mensual'), 0, 'Informes');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 0, 'Conciliaciones');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaciones'), 32992, 'M.N.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Conciliaciones'), 32993, 'D&oacute;lares');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 32820, 'Mensual Panamericana');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 32817, 'Ingresos Mensual por Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 32818, 'Resumen Mensual');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 32819, 'Resumen Diferencias-Cajas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 32789, 'Diario de Diferencias-Cajas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Informes'), 32838, 'Diferencias-Caja x Tienda');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Proceso Mensual'), 0, 'Despues del cierre');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Despues del cierre'), 32927, 'Reportes de envios de dinero');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Despues del cierre'), 32928, 'Reporte x fechas C.F.E.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Despues del cierre'), 32929, 'Reporte compra-venta dolares');

-- Tercera opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 0, 'Catalogos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 32803, 'Tiendas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 32802, 'Bancos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 32812, 'Ciudades');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 32828, 'Afiliaciones Bancarias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 32921, 'Actualizaci&oacute;n Ciudades');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 32922, 'Actualizaci&oacute;n Centros');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Catalogos'), 33009, 'Consultar Tienda');

-- Cuarta opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 0, 'Utilerias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32791, 'Captura de Fecha Actual');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32796, 'Borrar Informaci&oacute;n Existente');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32804, 'Configuraci&oacute;n');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32853, 'Checa Duplicados');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32854, 'Checar Archivos XL y CI');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32956, 'Correo Micro');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 0, 'Solicitud de Voucher');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Solicitud de Voucher'), 32958, 'Captura de Solicitudes');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Solicitud de Voucher'), 32959, 'Consulta de Solicitudes');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Solicitud de Voucher'), 32981, 'Bajar Archivo');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Utilerias'), 32983, 'Menu Nuevo');

-- Quinta opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 0, 'Procesos Varios');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Pasar Depositos M.N.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos M.N.'), 32813, 'Pasar Depositos Bancomer M.N.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos M.N.'), 32890, 'Pasar Depositos Bancrecer M.N.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos M.N.'), 32889, 'Pasar Depositos Santander M.N.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos M.N.'), 0, 'Depositos Bancoppel');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Bancoppel'), 32986, 'Depositos Bancoppel Efectivo');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Bancoppel'), 32988, 'Depositos Bancoppel Cheques');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Bancoppel'), 32987, 'Depositos Bancoppel Tarjetas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Bancoppel'), 32994, 'Depositos Corresponsales');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos M.N.'), 32999, 'Transferencias Bancoppel');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos M.N.'), 33011, 'Depositos Tarjetas Amex');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Pasar Depositos Dlls.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos Dlls.'), 32814, 'Pasar Depositos Bancomer Dlls.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos Dlls.'), 32892, 'Pasar Depositos Bancrecer Dlls.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pasar Depositos Dlls.'), 32893, 'Pasar Depositos Santander Dlls.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Consultas Depositos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consultas Depositos'), 32797, 'Moneda Nacional');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consultas Depositos'), 32815, 'Dolar');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Captura Depositos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Captura Depositos'), 32822, 'Moneda Nacional');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Captura Depositos'), 32831, 'Dolar');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Depositos Tarjetas Bancarias');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Tarjetas Bancarias'), 32825, 'Copiar Informacion Banamex');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Tarjetas Bancarias'), 32876, 'Copiar Informacion Bancomer');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Depositos Tarjetas Bancarias'), 0, 'Reportes');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT MAX(idu_si_modulo) FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32827, 'Movtos Tarjetas Por Bloque');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT MAX(idu_si_modulo) FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32829, 'Mensual Tarjetas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT MAX(idu_si_modulo) FROM cat_si_modulos WHERE nom_modulo = 'Reportes'), 32840, 'Diario de Tarjetas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Archivo Cobranza');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Archivo Cobranza'), 32834, 'Reporte Mensual');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 32835, 'Documentos Recibidos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Pase de P&oacute;lizas a Contabilidad');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pase de P&oacute;lizas a Contabilidad'), 32823, 'Poliza DIaria Ingresos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Pase de P&oacute;lizas a Contabilidad'), 32896, 'Poliza Falt-Sobr Banco');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Copias para Archivos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Copias para Archivos'), 32842, 'Impresi&oacute;n de Copia para Archivo');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Copias para Archivos'), 32871, 'Faltantes y Sobrantes Rep. x el Banco');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 32857, 'Copiar Depositos Bital');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 32857, 'Reporte Reembolso Caja Chica');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Reporte Bancomer');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte Bancomer'), 32910, 'Moneda Nacional 15129');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte Bancomer'), 32906, 'Moneda Nacional 665304');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte Bancomer'), 32909, 'Dolares 29873');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reporte Bancomer'), 32907, 'Dolares 1426338');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Otras Consultas');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Otras Consultas'), 32887, 'Consulta Pago Nomina');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Otras Consultas'), 32894, 'Consulta recibidos de Agua y Luz');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Otras Consultas'), 0, 'Consulta Pagos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consulta Pagos'), 32905, 'Alestra');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consulta Pagos'), 32903, 'Axtel');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consulta Pagos'), 32902, 'Cfe');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consulta Pagos'), 32904, 'Sofol');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Consulta Pagos'), 32888, 'Telmex');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Otras Consultas'), 32911, 'Consulta de Otros Ingresos');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 32897, 'Cambio Tda Tarjeta Conciliar');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 0, 'Reportes C.F.E.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes C.F.E.'), 32898, 'Reporte Liquidacion C.F.E.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Reportes C.F.E.'), 32913, 'Reporte Detalle C.F.E.');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 32914, 'Balanza de Faltante y Sobrante');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 32917, 'Reporte x Fechas Telmex');
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Procesos Varios'), 33017, 'Reporte de Tiendas Por Banco');

-- Sexta opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo, opc_modulo_obligatorio) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 0, 'Ayuda', 1::BIT);
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo, opc_modulo_obligatorio) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Ayuda'), 57664, 'Acerca de si...', 1::BIT); -- No se encontro clv_modulo_si
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo, opc_modulo_obligatorio) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Ayuda'), 32781, 'Ayuda de SI', 1::BIT);

-- Septima opcion del menú desplegable
INSERT INTO cat_si_modulos (idu_si_modulo_padre, clv_modulo_si, nom_modulo, opc_modulo_obligatorio) VALUES ((SELECT idu_si_modulo FROM cat_si_modulos WHERE nom_modulo = 'Raiz'), 57665, 'Salir', 1::BIT);
