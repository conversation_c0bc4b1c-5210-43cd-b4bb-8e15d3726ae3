CREATE OR REPLACE FUNCTION public.fun_obtener_usuarios_sistema_web_ingresos(
    IN p_busqueda text DEFAULT ''::text,
    IN p_limit integer DEFAULT  10,
    IN p_offset integer DEFAULT  0
)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para obtener los usuarios del sistema ingresos
-- ========================================================================
RETURNS JSON
LANGUAGE 'plpgsql'
VOLATILE SECURITY DEFINER
PARALLEL UNSAFE
COST 100
AS $BODY$
BEGIN
	RETURN (
        SELECT
        JSON_BUILD_OBJECT (
            'total',
            (
                SELECT COUNT ( * ) FROM cat_si_usuarios csu
                WHERE csu.opc_estatus = 1::BIT
                AND CASE WHEN p_busqueda != '' THEN nom_usuario ILIKE '%' || p_busqueda || '%' ELSE TRUE END
            ),
            'registros',
            JSON_AGG (
                JSON_BUILD_OBJECT (
                    'idu_si_usuario',
                    consulta.idu_si_usuario,
                    'num_empleado',
                    consulta.clv_numemp,
                    'nom_usuario',
                    consulta.nom_usuario,
                    'perfil',
                    JSON_BUILD_OBJECT (
                        'idu_si_rol',
                        consulta.idu_si_rol,
                        'nom_rol',
                        consulta.nom_rol
                    ),
                    'nom_puesto',
                    consulta.nom_puesto,
                    'num_telefono',
                    consulta.num_telefono,
                    'opc_activo',
                    CASE consulta.opc_activo WHEN 1::BIT THEN TRUE ELSE FALSE END
                )
            )
        )
        FROM (
            SELECT
                csu.idu_si_usuario,
                csu.clv_numemp,
                csu.nom_usuario,
                csr.idu_si_rol,
                csr.nom_rol,
                csu.nom_puesto,
                csu.num_telefono,
                csu.opc_activo
            FROM
                cat_si_usuarios csu
            INNER JOIN
                cat_si_roles csr ON csu.idu_si_rol = csr.idu_si_rol
            WHERE csu.opc_estatus = 1::BIT
            AND CASE WHEN p_busqueda != '' THEN csu.nom_usuario ILIKE '%' || p_busqueda || '%' ELSE TRUE END
            ORDER BY csu.fec_registro
            LIMIT p_limit
            OFFSET p_offset
        ) consulta
	);
END;
$BODY$;
