CREATE OR REPLACE FUNCTION fun_upsert_roles_sistema_web_ingresos(
	p_idu_si_rol INT DEFAULT NULL,
    p_nom_rol TEXT DEFAULT NULL,
	p_des_rol TEXT DEFAULT NULL,
	p_opc_activo BOOLEAN DEFAULT NULL,
	p_modulos JSONB DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 09/01/2024
-- Descripcion General: Funcion que inserta o actualiza (si se manda el identificador) un registro en la cat_si_roles y cat_si_rol_permisos (si se mandan modulos a otorgar permisos).
-- ========================================================================

RETURNS JSON AS

$BODY$
DECLARE
    esActualizacion BOOLEAN := p_idu_si_rol IS NOT NULL;
    esDuplicado BOOLEAN := FALSE;
    registro_insertado INT := 0;
    tieneModulos BOOLEAN := p_modulos IS NOT NULL;
	modulo_record JSONB;
BEGIN

	esDuplicado := EXISTS (
        SELECT 1 FROM cat_si_roles
        WHERE LOWER(nom_rol) = LOWER(p_nom_rol)
        AND CASE WHEN esActualizacion THEN
                idu_si_rol <> p_idu_si_rol
            ELSE
                TRUE
            END
        AND opc_estatus != 0::BIT
    );

    IF esDuplicado THEN
        RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'El nombre de perfil ya se encuentra registrado, intente con otro.');
    END IF;

    IF esActualizacion THEN -- Actualizar
        UPDATE cat_si_roles
        SET nom_rol = COALESCE(p_nom_rol, nom_rol),
            des_rol = COALESCE(p_des_rol, des_rol),
            opc_activo = CASE WHEN p_opc_activo THEN 1::BIT ELSE 0::BIT END,
            fec_actualizacion =  CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE
        WHERE idu_si_rol = p_idu_si_rol;
	ELSE -- Insertar
        INSERT INTO cat_si_roles (
            nom_rol,
            des_rol,
            opc_activo
        ) VALUES (
            p_nom_rol,
            p_des_rol,
            CASE WHEN p_opc_activo THEN 1::BIT ELSE 0::BIT END
        )
        RETURNING idu_si_rol INTO p_idu_si_rol; -- Recupera el nuevo idu_si_rol generado
	END IF;

    DELETE FROM cat_si_rol_permisos WHERE idu_si_rol = p_idu_si_rol;

    -- Insertar o actualizar en cat_si_rol_permisos para cada modulo
    FOR modulo_record IN SELECT * FROM jsonb_array_elements(p_modulos)
    LOOP
        INSERT INTO cat_si_rol_permisos (idu_si_rol, idu_si_modulo, opc_solo_lectura)
        VALUES (
            p_idu_si_rol,
            (modulo_record->>'idu_si_modulo')::INT,
            CASE WHEN (modulo_record->>'opc_solo_lectura')::BOOLEAN THEN 1::BIT ELSE 0::BIT END
        );
    END LOOP;

	-- Si la insercion se realiza correctamente, establecer el valor en TRUE
    GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', CASE WHEN esActualizacion THEN 'Registro actualizado correctamente.' ELSE 'Registro insertado correctamente.' END);
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro.' ELSE 'Error al insertar el registro.' END);
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro.' ELSE 'Error al insertar el registro.' END);

END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_upsert_roles_sistema_web_ingresos(
	p_idu_si_rol INT,
    p_nom_rol TEXT,
	p_des_rol TEXT,
	p_opc_activo BOOLEAN,
	p_modulos JSONB
) IS 'Funcion que inserta o actualiza (si se manda el identificador) un registro en la cat_si_roles y cat_si_rol_permisos (si se mandan modulos a otorgar permisos).
p_idu_si_rol = Identificador del rol a actualizar
p_nom_rol = Nombre del rol
p_des_rol = Descripcion del rol
p_opc_activo = Estatus del rol
p_modulos = Arreglo de identificadores de modulos
';
