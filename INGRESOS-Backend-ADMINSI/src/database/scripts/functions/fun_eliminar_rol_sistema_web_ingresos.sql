CREATE OR REPLACE FUNCTION fun_eliminar_rol_sistema_web_ingresos (p_idu_si_rol INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para eliminar lógicamente
-- un perfil del sistema de ingresos
-- ========================================================================

	RETURNS JSON AS

$BODY$
DECLARE
  registros_eliminados INT := 0;
BEGIN
  IF EXISTS(SELECT idu_si_usuario FROM cat_si_usuarios WHERE idu_si_rol = p_idu_si_rol AND opc_estatus = 1::BIT) THEN
    RETURN JSON_BUILD_OBJECT(
        'estatus', FALSE,
        'mensaje', 'No se puede eliminar el perfil debido a que se encuentra asociado a usuarios.'
    );
  END IF;



  UPDATE cat_si_roles
  SET opc_estatus = 0::BIT,
    fec_actualizacion = CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE
  WHERE idu_si_rol = p_idu_si_rol;

  -- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registros_eliminados = ROW_COUNT;

  -- Devolver el resultado (estatus: TRUE si se eliminó con éxito, estatus: FALSE si hubo un error)
	RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', 'Se eliminó el registro correctamente.');

  EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al eliminar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', 'No se puede eliminar el perfil debido a que se encuentra asociado a usuarios.');
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_eliminarperfilmonitores(p_idu_perfil INT) IS 'Esta función sirve para eliminar lógicamente
-- un perfil del sistema de ingresos,
idu_si_rol = Identificador del perfil a eliminar';
