DROP FUNCTION IF EXISTS fun_consulta_modulos_empleado_si(VARCHAR);
CREATE OR REPLACE FUNCTION fun_consulta_modulos_empleado_si(numempleado VARCHAR)
  RETURNS TABLE (
	clv_modulo_si		INT, 
	nom_modulo			TEXT,
	opc_solo_lectura	INT
)
LANGUAGE plpgsql AS
$BODY$
DECLARE
--	=============================================================================================================
--	Autor: Gaman Solutions
--	Fecha: 01/2024
--	BD: ingresos
--	Descripcion: Obtener listado de modulos permitidos para el empleado en sistema de ingresos (SI)
--	==============================================================================================================
BEGIN
	RETURN QUERY 
    SELECT
        CSM.clv_modulo_si,
		CSM.nom_modulo,
		CSRP.opc_solo_lectura::INT
	FROM cat_si_modulos CSM
	INNER JOIN cat_si_rol_permisos CSRP ON (CSM.idu_si_modulo = CSRP.idu_si_modulo)
	INNER JOIN cat_si_usuarios CSU ON (CSRP.idu_si_rol = CSU.idu_si_rol)
	WHERE 1=1
		AND CSU.clv_numemp = numempleado
		AND CSM.opc_estatus = B'1'
		AND CSRP.opc_estatus = B'1'
		AND CSU.opc_estatus = B'1'
	UNION ALL
	SELECT DISTINCT
		CSM.clv_modulo_si,
		CSM.nom_modulo,
		0
	FROM cat_si_modulos CSM
	WHERE 1=1
		AND CSM.opc_modulo_obligatorio = B'1';
	
END;
$BODY$;