CREATE OR REPLACE FUNCTION public.fun_autenticar_gestor_sistema_web_ingresos(IN p_num_empleado integer)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para validar que el usuario que ingresa al sistema exista en el catalogo de gestores
-- ========================================================================
    RETURNS json
    LANGUAGE 'plpgsql'
    VOLATILE SECURITY DEFINER
    PARALLEL UNSAFE
    COST 100
AS $BODY$
BEGIN
    RETURN 
        (SELECT
        JSON_BUILD_OBJECT(
            'num_empleado', COALESCE(cgsu.clv_numemp, ''),
            'nom_usuario', COALESCE(cgsu.nom_usuario, '')
        )
        FROM cat_gestor_si_usuarios cgsu
        WHERE cgsu.clv_numemp = p_num_empleado::VARCHAR
        AND opc_estatus 1::BIT
    );
END;
$BODY$;