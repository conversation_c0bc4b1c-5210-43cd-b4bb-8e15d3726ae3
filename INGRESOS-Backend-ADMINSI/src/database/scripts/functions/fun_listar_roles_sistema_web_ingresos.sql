CREATE OR REPLACE FUNCTION public.fun_listar_roles_sistema_web_ingresos(IN p_busqueda text DEFAULT ''::text, IN p_limit integer DEFAULT 10, IN p_offset integer DEFAULT  0)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para obtener la lista de perfiles
-- ========================================================================
    RETURNS json
    LANGUAGE 'plpgsql'
    VOLATILE SECURITY DEFINER
    PARALLEL UNSAFE
    COST 100
AS $BODY$
DECLARE
BEGIN
	RETURN (
		SELECT
            JSON_BUILD_OBJECT (
                'total',
                (
                    SELECT COUNT ( * )
                    FROM cat_si_roles
                    WHERE opc_estatus = 1::BIT
                        AND CASE WHEN p_busqueda != '' THEN nom_rol ILIKE '%' || p_busqueda || '%' ELSE TRUE END
                ),
                'registros',
                COALESCE(JSON_AGG (
                    JSON_BUILD_OBJECT (
                        'idu_si_rol',
                        consulta.idu_si_rol,
                        'nom_rol',
                        consulta.nom_rol,
                        'des_rol',
                        consulta.des_rol,
                        'opc_activo',
                        CASE consulta.opc_activo WHEN 1::BIT THEN TRUE ELSE FALSE END
                    )
                ) , '[]')
            )
        FROM (
            SELECT
                csr.idu_si_rol,
                csr.nom_rol,
                csr.des_rol,
                csr.opc_activo
            FROM
                cat_si_roles csr
            WHERE
                csr.opc_estatus = 1::BIT
                AND CASE WHEN p_busqueda != '' THEN csr.nom_rol ILIKE '%' || p_busqueda || '%' ELSE TRUE END
            ORDER BY csr.fec_registro
            LIMIT p_limit
                OFFSET p_offset
        ) consulta
	);
END;
$BODY$;
