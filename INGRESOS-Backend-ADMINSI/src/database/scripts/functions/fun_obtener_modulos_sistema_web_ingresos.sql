CREATE OR REPLACE FUNCTION public.fun_obtener_modulos_sistema_web_ingresos()
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para obtener los modulos del sistema ingresos
-- ========================================================================
  RETURNS json
LANGUAGE 'plpgsql'
VOLATILE SECURITY DEFINER
PARALLEL UNSAFE
COST 100
AS $BODY$
BEGIN
  RETURN (
    SELECT JSON_AGG(
      JSON_BUILD_OBJECT(
        'idu_si_modulo', csm.idu_si_modulo,
        'nom_modulo', csm.nom_modulo,
        'idu_si_modulo_padre', csm.idu_si_modulo_padre
      )
    ) as resultado
    FROM cat_si_modulos csm
    WHERE 
      idu_si_modulo != 0 
      AND opc_estatus = 1::BIT
      AND opc_modulo_obligatorio != 1::BIT
  );
END;
$BODY$;