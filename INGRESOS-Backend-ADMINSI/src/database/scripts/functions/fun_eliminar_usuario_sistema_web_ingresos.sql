CREATE OR REPLACE FUNCTION fun_eliminar_usuario_sistema_web_ingresos (p_idu_si_usuario INT)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para eliminar lógicamente
-- un perfil del sistema de ingresos
-- ========================================================================

	RETURNS BOOLEAN AS

$BODY$
DECLARE
  registros_eliminados INT := 0;
BEGIN

  UPDATE cat_si_usuarios
  SET opc_estatus = 0::BIT,
    fec_actualizacion = CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE
  WHERE idu_si_usuario = p_idu_si_usuario;
	
  -- Si la inserción se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registros_eliminados = ROW_COUNT;

  -- Devolver el resultado (TRUE si se insertó con éxito, FALSE si hubo un error)
	RETURN registros_eliminados::BOOLEAN;

  EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al eliminar registro: %', SQLERRM;
	RETURN registros_eliminados::BOOLEAN;
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_eliminar_usuario_sistema_web_ingresos(p_idu_perfil INT) IS 'Esta función sirve para eliminar lógicamente un usuario del sistema de ingresos,
idu_si_usuario = Identificador del perfil a eliminar';
