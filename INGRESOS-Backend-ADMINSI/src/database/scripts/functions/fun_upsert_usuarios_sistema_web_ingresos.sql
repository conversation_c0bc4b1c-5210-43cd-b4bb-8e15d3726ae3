CREATE OR REPLACE FUNCTION fun_upsert_usuarios_sistema_web_ingresos(
    p_idu_si_usuario INT DEFAULT NULL,
    p_idu_si_rol INT DEFAULT NULL,
	p_num_empleado CHARACTER(8) DEFAULT '',
    p_nom_empleado TEXT DEFAULT '',
    p_nom_puesto TEXT DEFAULT NULL,
	p_num_telefono VARCHAR(10) DEFAULT NULL,
    p_opc_activo BOOLEAN DEFAULT NULL
)

-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 09/01/2024
-- Descripcion General: Funcion que inserta o actualiza (si se manda el identificador) un registro en la cat_si_usuarios.
-- ========================================================================

RETURNS JSON AS

$BODY$
DECLARE
  esActualizacion BOOLEAN := p_idu_si_usuario IS NOT NULL;
  esDuplicado BOOLEAN := FALSE;
  registro_insertado INT := 0;
BEGIN

    -- Obtener validacion si es un usuario ya registrado
	esDuplicado := EXISTS (
        SELECT 1
        FROM cat_si_usuarios
        WHERE
            clv_numemp = p_num_empleado
        AND CASE WHEN esActualizacion THEN
                idu_si_usuario <> p_idu_si_usuario
            ELSE
                TRUE
            END
        AND opc_estatus = 1::BIT
    );

    -- Informar en caso de ser un usuario registrado
    IF esDuplicado THEN
        RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', 'El empleado ya se encuentra registrado, favor de verificar.');
    END IF;

    -- Actualizar usuario
	IF esActualizacion THEN
        UPDATE cat_si_usuarios
        SET idu_si_rol = COALESCE(p_idu_si_rol, idu_si_rol),
            opc_activo = COALESCE(CASE p_opc_activo WHEN TRUE THEN 1::BIT ELSE 0::BIT END, opc_activo),
            fec_actualizacion = CURRENT_TIMESTAMP::TIMESTAMP WITHOUT TIME ZONE
        WHERE idu_si_usuario = p_idu_si_usuario;
    -- Registrar usuario
	ELSE
	    INSERT INTO cat_si_usuarios (
            idu_si_rol,
            clv_numemp,
            nom_usuario,
            nom_puesto,
            num_telefono,
            opc_activo
        )
        VALUES (
            p_idu_si_rol,
            p_num_empleado,
            p_nom_empleado,
            p_nom_puesto,
            p_num_telefono,
            p_opc_activo::INT::BIT
        )
	    RETURNING idu_si_usuario INTO p_idu_si_usuario; -- Recupera el nuevo idu_si_usuario generado
	END IF;

	-- Si la insercion se realiza correctamente, establecer el valor en TRUE
	GET DIAGNOSTICS registro_insertado = ROW_COUNT;

    -- Devolver la respuesta
    IF registro_insertado > 0 THEN
        RETURN JSON_BUILD_OBJECT('estatus', TRUE, 'mensaje', CASE WHEN esActualizacion THEN 'Registro actualizado correctamente.' ELSE 'Registro insertado correctamente.' END);
    ELSE
        RETURN JSON_BUILD_OBJECT('estatus', FALSE, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro.' ELSE 'Error al insertar el registro.' END);
    END IF;

	EXCEPTION -- Capturar y manejar errores
		WHEN OTHERS THEN RAISE NOTICE 'Error al insertar / actualizar registro: %', SQLERRM;
	RETURN JSON_BUILD_OBJECT('estatus', false, 'mensaje', CASE WHEN esActualizacion THEN 'Error al actualizar el registro.' ELSE 'Error al insertar el registro.' END);
END;
$BODY$
LANGUAGE 'plpgsql' VOLATILE SECURITY DEFINER;

COMMENT ON FUNCTION fun_upsert_usuarios_sistema_web_ingresos(
	p_idu_si_usuario INT,
    p_idu_si_rol INT,
    p_num_empleado CHARACTER(8),
    p_nom_empleado TEXT,
    p_nom_puesto TEXT,
    p_num_telefono VARCHAR(10),
    p_opc_activo BOOLEAN

) IS 'Funcion que inserta o actualiza (si se manda el identificador) un registro en la cat_si_usuarios
p_idu_si_usuario = Identificador del usuario
p_num_empleado = Numero de empleado
p_nom_empleado= Nombre del empleado
p_nom_puesto= Nombre del puesto
p_num_telefono= Número de telefono
p_idu_si_rol = Identificador del rol
p_opc_activo = Estatus del usuario
';
