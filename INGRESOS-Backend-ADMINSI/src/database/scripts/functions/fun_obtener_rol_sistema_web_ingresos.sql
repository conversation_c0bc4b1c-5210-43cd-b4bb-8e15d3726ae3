CREATE OR REPLACE FUNCTION public.fun_obtener_rol_sistema_web_ingresos(IN p_idu_si_rol integer)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para obtener el perfil con base al idu de parametro
-- ========================================================================
    RETURNS json
    LANGUAGE 'plpgsql'
    VOLATILE SECURITY DEFINER
    PARALLEL UNSAFE
    COST 100
AS $BODY$
DECLARE
    modulos JSON := (
        SELECT COALESCE(
            JSON_AGG(
                JSON_BUILD_OBJECT(
                    'idu_si_modulo', csrp.idu_si_modulo,
                    'nom_modulo', csm.nom_modulo,
                    'opc_solo_lectura', CASE csrp.opc_solo_lectura WHEN 1::BIT THEN TRUE ELSE FALSE END)
                ),
            '[]')
        FROM cat_si_rol_permisos csrp
        INNER JOIN cat_si_modulos csm ON csrp.idu_si_modulo = csm.idu_si_modulo
        WHERE csrp.idu_si_rol = p_idu_si_rol
        AND csrp.opc_estatus = 1::BIT
    );
BEGIN
	RETURN (
        SELECT
            JSON_BUILD_OBJECT(
                'idu_si_rol', csr.idu_si_rol,
                'nom_rol', csr.nom_rol,
                'des_rol', csr.des_rol,
                'opc_activo', CASE csr.opc_activo WHEN 1::BIT THEN TRUE ELSE FALSE END,
                'modulos', modulos
            ) as resultado
        FROM
        cat_si_roles csr
        WHERE
        csr.opc_estatus = 1::BIT
        AND csr.idu_si_rol > 0
        AND csr.idu_si_rol = p_idu_si_rol
	);
END;
$BODY$;
