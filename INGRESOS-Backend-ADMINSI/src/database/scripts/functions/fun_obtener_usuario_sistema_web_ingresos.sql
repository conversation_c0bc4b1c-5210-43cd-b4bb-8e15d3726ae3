CREATE OR REPLACE FUNCTION public.fun_obtener_usuario_sistema_web_ingresos(IN p_idu_si_usuario integer)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para obtener el usuario registrado con base a su idu de parametro
-- ========================================================================
    RETURNS json
    LANGUAGE 'plpgsql'
    VOLATILE SECURITY DEFINER
    PARALLEL UNSAFE
    COST 100
AS $BODY$
BEGIN
	RETURN (
		SELECT
		  JSON_BUILD_OBJECT(
			'idu_si_usuario', csu.idu_si_usuario,
			'num_empleado', csu.clv_numemp,
			'idu_si_rol', csrp.idu_si_rol,
			'nom_rol', csrp.nom_rol,
			'nom_empleado', csu.nom_usuario,
			'nom_puesto', csu.nom_puesto,
			'num_telefono', csu.num_telefono,
			'opc_activo', CASE csu.opc_activo WHEN 1::BIT THEN TRUE ELSE FALSE END,
			'modulos', (
                SELECT 
					JSON_AGG(
						JSON_BUILD_OBJECT(
							'idu_si_modulo', csm.idu_si_modulo, 
							'nom_modulo', csm.nom_modulo, 
							'opc_solo_lectura', CASE csrp.opc_solo_lectura WHEN 1::BIT THEN TRUE ELSE FALSE END
						)
					) 
                FROM cat_si_rol_permisos csrp 
                INNER JOIN cat_si_modulos csm ON csrp.idu_si_modulo = csm.idu_si_modulo 
                WHERE csrp.idu_si_rol = csu.idu_si_rol
				AND csrp.opc_estatus = 1::BIT)
		) AS resultado
		FROM
		    cat_si_usuarios csu 
        INNER JOIN cat_si_roles csrp ON csu.idu_si_rol = csrp.idu_si_rol
		WHERE
		  csu.opc_estatus = 1::BIT
		  AND csu.idu_si_usuario = p_idu_si_usuario
	);
END;
$BODY$;