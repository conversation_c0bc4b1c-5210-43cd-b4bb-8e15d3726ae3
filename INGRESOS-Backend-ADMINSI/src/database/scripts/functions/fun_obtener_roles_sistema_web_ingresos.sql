CREATE OR REPLACE FUNCTION public.fun_obtener_roles_sistema_web_ingresos(IN p_busqueda text DEFAULT ''::text)
-- ========================================================================
-- Autor: Gaman Solutions
-- Fecha: 10/01/2024
-- Descripción General: Esta función sirve para obtener los perfiles junto a sus permisos en los modulos
-- ========================================================================
    RETURNS json
    LANGUAGE 'plpgsql'
    VOLATILE SECURITY DEFINER
    PARALLEL UNSAFE
    COST 100
AS $BODY$
DECLARE
BEGIN
	RETURN (
		SELECT
            JSON_BUILD_OBJECT (
                'total',
                (
                    SELECT COUNT ( * )
                    FROM cat_si_roles
                    WHERE opc_activo = 1::BIT
                    AND opc_estatus = 1::BIT
                    AND CASE WHEN p_busqueda != '' THEN nom_rol ILIKE '%' || p_busqueda || '%' ELSE TRUE END
                ),
                'registros',
                COALESCE(JSON_AGG (
                    JSON_BUILD_OBJECT (
                        'idu_si_rol',
                        consulta.idu_si_rol,
                        'nom_rol',
                        consulta.nom_rol,
                        'des_rol',
                        consulta.des_rol,
                        'opc_activo',
                        CASE consulta.opc_activo WHEN 1::BIT THEN TRUE ELSE FALSE END,
                        'modulos',
                        (
                            SELECT
                                COALESCE(JSON_AGG(JSON_BUILD_OBJECT(
                                    'idu_si_modulo', csm.idu_si_modulo,
                                    'nom_modulo', csm.nom_modulo,
                                    'opc_solo_lectura', CASE csrp.opc_solo_lectura WHEN 1::BIT THEN TRUE ELSE FALSE END)),
                                '[]')
                            FROM cat_si_rol_permisos csrp
                            INNER JOIN cat_si_modulos csm ON csm.idu_si_modulo = csrp.idu_si_modulo
                            WHERE csrp.idu_si_rol = consulta.idu_si_rol
                        )
                    )
                ) , '[]')
            )
        FROM (
            SELECT
                csr.idu_si_rol,
                csr.nom_rol,
                csr.des_rol,
                csr.opc_activo
            FROM
                cat_si_roles csr
            WHERE
                csr.opc_activo = 1::BIT
                AND csr.opc_estatus = 1::BIT
                AND CASE WHEN p_busqueda != '' THEN csr.nom_rol ILIKE '%' || p_busqueda || '%' ELSE TRUE END
        ) consulta
	);
END;
$BODY$;
