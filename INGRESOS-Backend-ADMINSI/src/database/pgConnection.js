import { Pool } from 'pg';
import createHttpError from 'http-errors';

import { log } from '../utilities/logger';
import { ObtenerCredencialesBd } from '../utilities/obtener-credenciales-bd';
import { CONEXION_BD_EXITOSA, CONEXION_DB_FALLIDA } from '../constants';

const DEV = process.env.NODE_ENV === 'development';
const LOG_QUERY = !!Number(process.env.LOG_QUERY);

/** @type {Pool} */
let pool;

async function establecerConexion() {
  const { CATEGORIA_INGRESOS: categoria } = process.env;
  let database;
  let host;

  try {
    const credenciales = await ObtenerCredencialesBd(categoria);

    database = credenciales.db;
    host = credenciales.host;

    const config = {
      ...credenciales,
      database,
      host,
      max: 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
      client_encoding: 'SQL_ASCII',
    };

    pool = new Pool(config);

    pool.on('error', (error) => {
      logger.log({ level: 'error', message: `${CONEXION_DB_FALLIDA(database, host)}\n${error?.message}` });
      process.exit(1);
    });

    const client = await pool.connect();

    logger.log({ level: 'server', message: CONEXION_BD_EXITOSA(database, host) });
    client.release();
  } catch (error) {
    logger.log({ level: 'error', message: `${CONEXION_DB_FALLIDA(database, host)}\n${error.message}` });
    process.exit(1);
  }
}

establecerConexion();

/**
 * Método para ejecutar una query de forma segura
 * @param {String} query Query a ejecutar
 * @param {Array<any>} [parametros] Parametros de la query
 * @returns {Promise<import('pg').QueryResult<any>>} Resultado de la query
 *
 * @example
 * const res = await query('SELECT * FROM usuarios WHERE id = $1', [1]);
 *
 * if (res.rowCount === 0) {
 *  return null;
 * }
 *
 * return res.rows[0];
 */
export const query = async (query, parametros = []) => {
  const start = Date.now();

  const res = await pool.query(query, parametros);

  if (DEV && LOG_QUERY) {
    const duracion = Date.now() - start;
    let formatQuery = query;

    for (let i = 0; i < parametros.length; i += 1) {
      const valor = typeof parametros[i] === 'string' ? `'${parametros[i]}'` : parametros[i];
      formatQuery = formatQuery.replace(`$${i + 1}`, valor);
    }
    log({
      isError: false,
      title: 'Consulta ejecutada',
      descripcion: formatQuery,
      objeto: { parametros, duracion: duracion / 1000, rows: res.rowCount },
    });
  }

  // Si la respuesta tiene un campo resultado y este es un objeto con un campo error, se trata de un error
  if (res.rows[0] && Object.prototype.hasOwnProperty.call(res.rows[0], 'resultado') && res.rows[0].resultado?.error) {
    if (res.rows[0].resultado.publico) {
      throw createHttpError(400, res.rows[0].resultado.error);
    }
    throw createHttpError(500, res.rows[0].resultado.error);
  }

  return res;
};

/**
 * Método para obtener un cliente de conexión
 *
 * @returns {Promise<import('pg').PoolClient>} Cliente de conexión
 */
export const getClient = async () => {
  const client = await pool.connect();
  const { query } = client;
  const { release } = client;

  const timeout = setTimeout(() => {
    logger.error('Un cliente ha estado en uso durante más de 5 segundos');
    logger.error(`La última consulta ejecutada en este cliente fue: ${client.lastQuery}`);
  }, 5000);

  client.query = (...args) => {
    client.lastQuery = args;
    return query.apply(client, args);
  };

  client.release = () => {
    clearTimeout(timeout);
    client.query = query;
    client.release = release;
    return release.apply(client);
  };

  return client;
};
