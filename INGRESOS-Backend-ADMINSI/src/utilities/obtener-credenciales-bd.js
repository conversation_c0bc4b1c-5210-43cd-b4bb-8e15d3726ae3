import axios from 'axios';

const {
  SITIBUNDUS: sitibundus,
  UNDIVAGO: undivago,
  USER_TYPE: usertype,
  URL_VALIDACION_TOKEN: rutaValidacion,
  URL_CREDENCIALES: urlCredenciales,
  URL_OBTENER_TOKEN: urlObtenerToken,
  APP_NAME: aplicacion,
} = process.env;

const category = 'OBTENERCREDENCIALES';
const name = 'REMEDIACIONES';
const type = 'OFA';

/**
 * Obtiene el token de autorización
 */
const ObtenerToken = () => axios.post(urlObtenerToken, {
  sitibundus,
  undivago,
  usertype: Number(usertype),
});

/**
 * Obtiene las credenciales de una base de datos por categoría
 * @param {number} clave
 * @returns {Promise}
 */
export const ObtenerCredencialesBd = async (clave) => {
  const { data: { id_token } } = await ObtenerToken();

  let { data: [credenciales] } = await axios.post(
    urlCredenciales,
    {
      category,
      name,
      type,
      aplicacion,
      categorias: {
        categoria: Number(clave),
      },
      rutaValidacion,
    },
    {
      headers: {
        Authorization: `Bearer ${id_token}`,
      },
    },
  );

  const charCode = '11297115115';

  credenciales = Object.entries(credenciales).reduce((acc, [key, value]) => {
    if ([...key].map((c) => c.charCodeAt(0)).join('') === charCode) {
      acc[`${key}word`] = value;
    } else {
      acc[key] = value;
    }
    return acc;
  }, {});

  return credenciales;
};
