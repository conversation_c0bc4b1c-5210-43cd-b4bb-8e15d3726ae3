import mime from 'mime-types';
import { networkInterfaces } from 'os';

/**
 * Función para obtener la dirección IP local
 * @returns {String} Dirección IP local
 */
export const obtenerIpLocal = () => {
  const nets = networkInterfaces();
  const results = Object.create(null);

  for (const name of Object.keys(nets)) {
    for (const net of nets[name]) {
      const familyV4Value = typeof net.family === 'string' ? 'IPv4' : 4;
      if (net.family === familyV4Value && !net.internal) {
        if (!results[name]) {
          results[name] = [];
        }
        results[name].push(net.address);
      }
    }
  }

  if (process.platform === 'darwin') {
    return results.en0[0];
  }
};

/**
 * Función para obtener el tipo de contenido de un archivo a partir de su extensión
 * @param {String} fileExtension Extensión del archivo
 * @returns {String} Tipo de contenido del archivo
 */
export const obtenerContentTypePorExtension = (fileExtension) => {
  const contentType = mime.contentType(fileExtension);
  return contentType || 'application/octet-stream'; // Tipo de contenido genérico si no se encuentra la extensión
};

/**
 * Función para obtener el tipo de contenido de un archivo a partir de su extensión
 * @param {String} fileExtension Extensión del archivo
 * @returns {Json} Tipo de contenido del archivo
 */
export const construirArbolModulos = (modulos) => {
  const mapa = new Map();

  // Crear un mapa para facilitar la búsqueda por id
  modulos.forEach((modulo) => {
    mapa.set(modulo.idu_si_modulo, { ...modulo, submodulos: [] });
  });

  // Construir la estructura del árbol
  const arbol = [];
  modulos.forEach((modulo) => {
    const actual = mapa.get(modulo.idu_si_modulo);
    const padre = mapa.get(modulo.idu_si_modulo_padre);

    if (padre) {
      padre.submodulos.push(actual);
    } else {
      arbol.push(actual);
    }
  });

  return arbol;
};
