/* eslint-disable max-len */
import chalk from 'chalk';

// Server
export const DEV_PUERTO_SERVIDOR = (port) => {
  if (process.env.NODE_ENV === 'development') {
    return `🚀 Servicio corriendo en puerto ${chalk.bold(port)}`;
  }
  return `🚀 Servicio corriendo en puerto => ${port}`;
};
export const DEV_ENV_SERVIDOR = `🌍 Entorno de ejecución => ${chalk.bold(process.env.NODE_ENV)}`;
export const CONEXION_BD_EXITOSA = (db, host) => `🎉 Conexión exitosa a la base de datos: ${chalk.bold(db)} host: ${chalk.bold(host)}`;
export const CONEXION_DB_FALLIDA = (db, host) => `💥 Error al intentar establecer conexión con la base de datos: ${chalk.bold(db)} host: ${chalk.bold(host)}`;
export const ERROR_SERVIDOR = 'Ha ocurrido un problema al procesar su solicitud, intente más tarde.';
export const NOT_FOUND = 'No fue posible localizar el recurso solicitado';
export const RATE_LIMIT = 'Se ha alcanzado el límite de peticiones, intente más tarde.';
export const SERVIDOR_NO_RESPONDE = (direccion) => `No se pudo recibir respuesta del servidor: ${direccion}`;

// Estatus 400
export const RECURSO_NO_ENCONTRADO = 'El recurso solicitado no ha podido ser localizado';
export const ERROR_REGISTRO = 'El recurso no ha podido ser registrado, favor de verificar la solicitud';
export const ERROR_ACTUALIZAR = 'El recurso no ha podido ser actualizado, favor de verificar la solicitud';
export const ERROR_ELIMINAR = 'El recurso no ha podido ser eliminado, favor de verificar la solicitud';

// Sesiones
export const ERROR_AUTENTICACION = 'Credenciales inválidas. Por favor, inicia sesión de nuevo.';
export const EMPLEADO_NO_AUTORIZADO = 'Acceso denegado, comunícate con el administrador del sistema para obtener asistencia.';
export const ERROR_VALIDAR_TOKEN = 'Error al validar el token';
export const NO_TOKEN_PROVIDED = 'No se proporcionó un token';
export const TOKEN_INVALIDO = 'El token proporcionado no es válido';
export const NO_IDP = 'No se obtuvo el método de autenticación';
export const TOKEN_INVALIDO_NUMERO_EMPLEADO = 'Token inválido: No se encontró el número de empleado en el token';
export const TOKEN_INVALIDO_SCOPE = 'Token inválido: No se encontró un scope válido en el token';

// Perfiles
export const PERFIL_CONFLICTO = 'Ya existe un perfil con el nombre proporcionado, intente otro.';

// Usuarios
export const USERNAME_CONFLICTO = 'El nombre de usuario no está disponible, intente otro.';

// Campos
export const CAMPO_ARREGLO = 'El campo {#label} debe ser un arreglo';
export const CAMPO_ARREGLO_VACIO = 'El arreglo {#label} no debe estar vacío';
export const CAMPO_EMAIL_INVALIDO = 'El campo {#label} no es un email válido';
export const CAMPO_FUERA_DE_DEFINICION = 'El campo {#label} no forma parte de la definición';
export const CAMPO_INVALIDO = 'El valor proporcionado en el campo {#label} no es válido';
export const CAMPO_NUMERICO = 'El valor proporcionado en el campo {#label} debe ser númerico';
export const CAMPO_NUMERICO_POSITIVO = 'El valor proporcionado en el campo {#label} debe ser mayor a 0';
export const CAMPO_REQUERIDO = 'El campo {#label} es requerido';
export const CAMPOS_CARACTERES_MAXIMOS = 'El campo {#label} no debe exceder los {#limit} caracteres';
export const CAMPOS_FALTANTES = 'Debes proporcionar al menos uno de los siguientes campos: {#peers}';
export const FORMATO_INVALIDO = 'El formato del campo {#label} no es válido';
export const VALOR_BOOLEANO_INVALIDO = 'El valor proporcionado para el campo {#label} debe ser 0 o 1';

// Parametros
export const PARAMETRO_REQUERIDO = 'El parámetro {#label} es requerido';
export const PARAMETRO_INVALIDO = 'El parámetro {#label} no es válido';

// Validación de datos
export const FORMATO_FECHA = 'El formato de fecha en el campo {#label} no es válido.';
export const REGISTROS_VACIOS = 'No se encontraron registros';
export const ESTATUS_INVALIDO = 'El estatus proporcionado no es válido';
export const ESTATUS_REQUERIDO = 'El estatus es requerido';
export const ESTATUS_FORMATO_INVALIDO = 'El formato de estatus proporcionado no es válido';
export const TIPO_CONCILIACION_INVALIDO = 'El tipo de conciliación proporcionado no es válido';
export const VALIDACION_NUMERO_ENTERO = 'El valor proporcionado debe ser un número entero';
