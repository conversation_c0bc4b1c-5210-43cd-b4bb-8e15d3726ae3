import Joi from 'joi';

import { opciones } from './utilidades-validacion';

export const esquemaPost = Joi.object({
  idu_si_rol: Joi
    .number()
    .integer()
    .positive()
    .required(),
  num_empleado: Joi
    .number()
    .integer()
    .positive()
    .required(),
  nom_usuario: Joi
    .string()
    .required(),
  num_telefono: Joi
    .string()
    .allow(''),
  nom_puesto: Joi
    .string()
    .required(),
  opc_activo: Joi
    .boolean(),
}).options(opciones);

export const esquemaPut = {
  params: Joi.object({
    idu: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opciones),
  body: Joi.object({
    idu_si_rol: Joi
      .number()
      .integer()
      .positive(),
    opc_activo: Joi
      .boolean(),
  })
    .or(
      'idu_si_rol',
      'opc_activo',
    )
    .options(opciones),
};
