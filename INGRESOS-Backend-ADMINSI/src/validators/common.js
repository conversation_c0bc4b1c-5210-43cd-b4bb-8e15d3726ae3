import Joi from 'joi';
import { opciones } from './utilidades-validacion';

export const esquemaListado = Joi.object({
  busqueda: Joi
    .string()
    .allow(''),
  registrosPorPagina: Joi
    .number()
    .integer()
    .positive(),
  pagina: Joi
    .number()
    .integer()
    .positive(),
}).options(opciones);

export const esquemaPorId = Joi.object({
  idu: Joi
    .number()
    .integer()
    .positive()
    .required(),
}).options(opciones);
