import Joi from 'joi';

import { opciones } from './utilidades-validacion';

const modulo = Joi.object().keys({
  idu_si_modulo: Joi.number().integer().positive(),
  opc_solo_lectura: Joi.boolean(),
});

export const esquemaPost = Joi.object({
  nom_rol: Joi
    .string()
    .max(50)
    .required(),
  des_rol: Joi
    .string()
    .required(),
  opc_activo: Joi
    .boolean(),
  modulos: Joi
    .array()
    .items(modulo),
}).options(opciones);

export const esquemaPut = {
  params: Joi.object({
    idu: Joi
      .number()
      .integer()
      .positive()
      .required(),
  }).options(opciones),
  body: Joi.object({
    idu_si_rol: Joi
      .number().integer().positive().required(),
    nom_rol: Joi
      .string()
      .max(50),
    des_rol: Joi
      .string(),
    opc_activo: Joi
      .boolean(),
    modulos: Joi
      .array()
      .items(modulo),
  })
    .or('nom_rol', 'des_rol', 'opc_activo')
    .options(opciones),
};
