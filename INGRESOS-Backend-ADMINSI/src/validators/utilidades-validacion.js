/* eslint-disable key-spacing */
import {
  CAMPOS_CARACTERES_MAXIMOS,
  CAMPOS_FALTANTES,
  CAMPO_ARREGLO,
  CAMPO_ARREGLO_VACIO,
  CAMPO_EMAIL_INVALIDO,
  CAMPO_FUERA_DE_DEFINICION,
  CAMPO_INVALIDO,
  CAMPO_NUMERICO,
  CAMPO_NUMERICO_POSITIVO,
  CAMPO_REQUERIDO,
  FORMATO_FECHA,
  PARAMETRO_INVALIDO,
  PARAMETRO_REQUERIDO,
} from '../constants/mensajes';

export const opciones = {
  messages: {
    'any.invalid'    : CAMPO_INVALIDO,
    'any.required'   : CAMPO_REQUERIDO,
    'number.base'    : CAMPO_NUMERICO,
    'number.integer' : CAMPO_INVALIDO,
    'number.positive': CAMPO_NUMERICO_POSITIVO,
    'object.unknown' : CAMPO_FUERA_DE_DEFINICION,
    'date.format'    : FORMATO_FECHA,
    'string.empty'   : CAMPO_REQUERIDO,
    'string.max'     : CAMPOS_CARACTERES_MAXIMOS,
    'string.email'   : CAMPO_EMAIL_INVALIDO,
    'boolean.base'   : CAMPO_INVALIDO,
    'object.missing' : CAMPOS_FALTANTES,
    'array.base'     : CAMPO_ARREGLO,
    'array.includesRequiredUnknowns': CAMPO_ARREGLO_VACIO,
  },
};

export const opcionesEnParams = {
  messages: {
    'any.invalid' : PARAMETRO_INVALIDO,
    'any.required': PARAMETRO_REQUERIDO,
    'number.base' : PARAMETRO_INVALIDO,
  },
};
