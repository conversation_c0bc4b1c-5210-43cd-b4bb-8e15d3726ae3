// Bootstrap overrides
//
// Color system
//

$white: #FDFDFD;
$gray-50: #fafafa;
$gray-100: #f8f9fa;
$gray-200: #eeeeee;
$gray-300: #e0e0e0;
$gray-350: #C7C7C7;
$gray-400: #bdbdbd;
$gray-500: #707070;
$gray-600: #757575;
$gray-700: #616161;
$gray-800: #424242;
$gray-900: #212121;
$black: #292930 !default;

$blue: #31346B !default;
$dark-blue: #014070 !default;
$blue-info: #00a9e0 !default;
$blue-secondary: #33404E !default;
$gray-seconday: #BEC6CD !default;
$indigo: #6610f2 !default;
$purple: #6f42c1 !default;
$pink: #e83e8c !default;
$red: #d60d2b !default;
$orange: #fe5000 !default;
$yellow: #ffdd35 !default;
$green: #409A8E !default;
$teal: #090b0a !default;
$cyan: #63c2de;
$blue-step: #1477D5;
$blue-light: #A3C4EB;
$red-error: #CC092E;
$red-error-light: #D47B8A;

$colors: (
  'blue': $blue,
  'indigo': $indigo,
  'purple': $purple,
  'pink': $pink,
  'red': $red,
  'orange': $orange,
  'yellow': $yellow,
  'green': $green,
  'teal': $teal,
  'cyan': $blue-info,
  'white': $white,
  'gray': $gray-600,
  'gray-dark': $gray-800,
  'blue-dark': $dark-blue,
);

$theme-colors: (
  'primary': $blue,
  'secondary': $gray-seconday,
  'success': $green,
  'info': $blue-info,
  'warning': $orange,
  'danger': $red,
  'light': $gray-200,
  'dark': $dark-blue,
);

// Options
//
// Quickly modify global styling by enabling or disabling optional features.

$enable-transitions: true;
$enable-rounded: true;
$enable-flex: true;
// Body
//
// Settings for the `<body>` element.

// $body-bg:                         #e4e5e6;
$body-bg: $white;
$body-color: $blue-secondary !default;

// Typography
//
// Font, line-height, and color for body text, headings, and more.

// $font-size-base:                  0.875rem;
$font-size-base: 1rem;
$font-family-base:  'Avenir', sans-serif;

// Breadcrumbs

$breadcrumb-bg: #fff;
$breadcrumb-margin-bottom: 1.5rem;

// Cards

$card-border-color: $gray-300;
$card-cap-bg: $gray-100;

// Dropdowns

$dropdown-padding-y: 0;
$dropdown-border-color: $gray-300;
$dropdown-divider-bg: $gray-100;

// Buttons
$btn-secondary-border: $gray-300;
$btn-padding-y: 0.6rem;
$btn-padding-x: 1rem;
$btn-border-radius: 0.4rem;
$btn-border: 1px solid #E1E7ED;

// Progress bars
$progress-bg: $gray-100;

// Tables
$table-bg-accent: $gray-100;
$table-bg-hover: $gray-100;
$table-cell-padding-y: 0;
$table-cell-padding-x: 0;

// Forms
$input-group-addon-bg: $gray-100;
$input-border-color: $gray-200;
$input-group-addon-border-color: $gray-200;
$input-padding-y: 0.6rem;
$input-padding-x: 1rem;
$input-border-radius: 0.4rem;
$input-bg: #F5F8FA;
$input-color: #585858;
$zindex-dropdown: 1025 !default;
$zindex-sticky: 1009 !default;
$form-check-input-checked-bg-color: $green;

// Pagination
$pagination-color: $gray-600;
$pagination-bg: $white;

$pagination-hover-color: $black;

$pagination-active-color: $black;
$pagination-active-bg: $white;
$pagination-active-border-color: $gray-300;

$custom-file-text: ();