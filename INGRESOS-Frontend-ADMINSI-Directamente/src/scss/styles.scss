/*!
 * CoreUI - Open Source Bootstrap Admin Template
 * @version v1.0.6
 * @link http://coreui.io
 * Copyright (c) 2017 creativeLabs <PERSON><PERSON><PERSON>
 * @license MIT
 */

// Import Core Ui Icons
// @import './material-variables';
// Import Core Ui Icons
@import './../../node_modules/@coreui/icons/css/all.min.css';

// Override Boostrap variables
@import 'bootstrap-variables';

// Import Bootstrap source files
@import './../../node_modules/bootstrap/scss/bootstrap';

// Import core styles
@import 'core/core';

// Custom styles
@import 'custom';

/* Importing Datepicker SCSS file. */
@import "node_modules/ngx-bootstrap/datepicker/bs-datepicker";

// regular style toast
@import 'ngx-toastr/toastr';

html, body { height: 100%; }
body { margin: 0; font-family: 'Avenir', sans-serif; }
label {
  margin-bottom: 0.5rem !important;
}

.wizard .wizard-content div[data-opened-step="false"] {
  display: none !important;
}

.contenedor{
  min-height: calc(100vh - 138px);
}


.fade-in {
  animation: animation-fade-in 0.3s ease-in-out;
}

.cursor-pointer {
  cursor: pointer;
}
 
.paper {
  background-color: #EAF0F6;
  border-radius: 6px;
}

.text-neutral {
  color: #4B5D6E;
  font-weight: 500;
}

@keyframes animation-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.list-group-item {
  border-radius: 0 !important;
}

.btn-icon {
  background-color: #EBEFF2;
  padding: 2px !important;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  text-align: center;
}

.icon-checkbox {
  width: min-content;
  margin: 0;
  padding: 0;
  display: flex;
  justify-content: center;
  align-items: center;

  .checkbox-icon-wrapper {
    background: $white;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1px solid #a9a9a9;
    border-radius: 20%;
    width: 19px;
    height: 19px;
    transition: background-color 0.2s ease-in-out;
    cursor: pointer;
    
    .checkbox-icon {
      font-size: 13px;
      font-weight: bold;
      line-height: 0;    
    }

  }

  input[type="checkbox"] {
    display: none;
    &:checked + .checkbox-icon-wrapper {
      background: $green; 
    }

    & + .checkbox-icon-wrapper > .checkbox-icon {
        opacity: 0;
        transform: scale(1);
        transition: all .1s ease-in;
    }

    &:checked + .checkbox-icon-wrapper > .checkbox-icon {
      transform: scale(1);
      opacity: 1;
    }
  }
}

.checkbox label input[type="checkbox"] + .checkbox-icon-wrapper > .checkbox-icon {
    opacity: 0;
    transform: scale(1);
    transition: all .1s ease-in;
}

.checkbox label input[type="checkbox"]:checked + .checkbox-icon-wrapper > .checkbox-icon {
  transform: scale(1);
  opacity: 1;
}

/* Importing Bootstrap SCSS file. */
@import "./node_modules/bootstrap/scss/bootstrap";
