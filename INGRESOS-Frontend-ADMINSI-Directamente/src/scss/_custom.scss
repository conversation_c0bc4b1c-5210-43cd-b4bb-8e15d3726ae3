// Here you can add other styles
thead > tr > th {
  span.sort-icon {
    cursor: pointer;
  }
}

.table .thead-light th {
  background-color: #f8f9fa;
}

.nav-dropdown-items {
  border-radius: 0;
}

.nav-dropdown-items.children {
  .nav-item > a {
    padding-left: 30px;
  }
}

.nav-dropdown-items.children {
  border-radius: 0 0 15px 15px;
  .nav-item {
    .nav-dropdown-items.children {
      .nav-item > a {
        padding-left: 45px;
      }
    }
  }
}

.nav-link-success,
.nav-link-danger,
.nav-link-info,
.nav-link-warning {
  color: $white !important;
}

.ui-growl {
  z-index: 9999 !important;
}

.open > .dropdown-menu {
  opacity: 1;
  visibility: visible;
}

.dropdown-menu:not(.show) {
  display: block;
  // opacity: 0;
  visibility: hidden;
  -webkit-transition: opacity 500ms ease, visibility 500ms ease;
  -moz-transition: opacity 500ms ease, visibility 500ms ease;
  -o-transition: opacity 500ms ease, visibility 500ms ease;
  transition: opacity 500ms ease, visibility 500ms ease;
}

//CURSOR-ALLOW
button:disabled {
  cursor: not-allowed;
}

.bread-menu {
  top: 101px !important;
  padding: 0.6rem 1rem;
}

.breadcrumb {
  border-radius: 0px !important;
}

.ui-widget a {
  font-size: 1em !important;
}

.card-header .card-actions a,
.card-header .card-actions button {
  min-width: 50px;
  padding: 0.75rem;
}

accordion-group > .card {
  margin-bottom: 0.5rem;
}
accordion-group .card-header {
  cursor: pointer;
}

.sortable-item {
  padding: 6px 12px;
  margin-bottom: 4px;
  font-size: 14px;
  line-height: 1.4em;
  text-align: center;
  cursor: grab;
  border: 1px solid transparent;
  border-radius: 4px;
  border-color: #adadad;
}

.sortable-item-active {
  background-color: #e6e6e6;
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.sortable-wrapper {
  min-height: 150px;
}

body .ui-paginator .ui-dropdown {
  height: inherit;
}

.sidebar-fixed .sidebar {
  border-right: 1px solid $gray-300;
}

//BTN-SECONDARY-CUSTOM
$blue-secondary-border: #bfe3fc;

.btn {
  font-size: 0.9em !important;
}

.btn-secondary {
  color: $blue !important;
}

.badge {
  font-size: 1.1em !important;
  border-radius: 0;
}

.btn-outline-secondary {
  color: $blue;
  border-color: $blue-secondary-border;
}

.btn-outline-secondary:hover {
  color: $blue;
  background-color: $blue-secondary;
  border-color: $blue-secondary-border;
}

.btn-outline-secondary:not(:disabled):not(.disabled).active,
.show > .btn-outline-secondary.dropdown-toggle {
  color: $blue;
  background-color: $blue-secondary;
  border-color: $blue-secondary-border;
}

.btn-outline-secondary.disabled,
.btn-outline-secondary:disabled {
  color: $blue;
  background-color: transparent;
}

.app-header {
  background-color: #707070;
}

.modal-body {
  border-radius: 0;
}

h1, h2, h3, h4, h5, h6 {
  margin: 0;
}

body {
  font-family: 'Avenir', sans-serif;
}

ul.pagination li.active > a { 
  font-weight: bolder !important; 
}

.cdk-overlay-container {
  z-index: 1060!important;
}

.btn-success {
  color: #FFF !important;
}