
// Custom Theming for Angular Material
// For more information: https://material.angular.io/guide/theming

@use '@angular/material' as mat;
@include mat.core();

/* For use in src/lib/core/theming/_palette.scss */
$primary-palette: (
    50 : #e0eef7,
    100 : #b3d4ea,
    200 : #80b7dc,
    300 : #4d9ace,
    400 : #2685c4,
    500 : #31346B,
    600 : #31346B,
    700 : #005caa,
    800 : #0052a2,
    900 : #004093,
    A100 : #bed6ff,
    A200 : #8bb5ff,
    A400 : #5894ff,
    A700 : #31346B,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #ffffff,
        500 : #ffffff,
        600 : #ffffff,
        700 : #ffffff,
        800 : #ffffff,
        900 : #ffffff,
        A100 : #000000,
        A200 : #000000,
        A400 : #000000,
        A700 : #ffffff,
    )
);

$secondary-palette: (
    50 : #fffbe7,
    100 : #fff5c2,
    200 : #ffee9a,
    300 : #ffe772,
    400 : #ffe253,
    500 : #ffdd35,
    600 : #ffd930,
    700 : #ffd428,
    800 : #ffcf22,
    900 : #ffc716,
    A100 : #ffffff,
    A200 : #fffefc,
    A400 : #fff1c9,
    A700 : #ffeaaf,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #000000,
        500 : #000000,
        600 : #000000,
        700 : #000000,
        800 : #000000,
        900 : #000000,
        A100 : #000000,
        A200 : #000000,
        A400 : #000000,
        A700 : #000000,
    )
);

$warn-palette: (
    50 : #f7e9e9,
    100 : #eac9c7,
    200 : #dda5a2,
    300 : #cf817c,
    400 : #c46660,
    500 : #ba4b44,
    600 : #b3443e,
    700 : #ab3b35,
    800 : #a3332d,
    900 : #94231f,
    A100 : #ffd0cf,
    A200 : #ff9f9c,
    A400 : #ff6e69,
    A700 : #ff554f,
    contrast: (
        50 : #000000,
        100 : #000000,
        200 : #000000,
        300 : #000000,
        400 : #000000,
        500 : #ffffff,
        600 : #ffffff,
        700 : #ffffff,
        800 : #ffffff,
        900 : #ffffff,
        A100 : #000000,
        A200 : #000000,
        A400 : #000000,
        A700 : #000000,
    )
);

$webclient-primary: mat.m2-define-palette($primary-palette);
$webclient-secondary: mat.m2-define-palette($secondary-palette, A200, A100, A400);
$webclient-warn: mat.m2-define-palette($warn-palette);

$global-typography: mat.m2-define-typography-config(
    $font-family:  'Avenir',
    $headline-1: mat.m2-define-typography-level(112px, 112px, 300, $letter-spacing: -0.05em),
    $headline-2: mat.m2-define-typography-level(56px, 56px, 400, $letter-spacing: -0.02em),
    $headline-3: mat.m2-define-typography-level(45px, 48px, 400, $letter-spacing: -0.005em),
    $headline-4: mat.m2-define-typography-level(34px, 40px, 400),
    $headline-5: mat.m2-define-typography-level(24px, 32px, 400),
    $headline-6: mat.m2-define-typography-level(20px, 32px, 400),
    $subtitle-1: mat.m2-define-typography-level(16px, 28px, 400),
    $body-1: mat.m2-define-typography-level(14px, 20px, 400),
    $body-2: mat.m2-define-typography-level(14px, 20px, 400),
    $subtitle-2: mat.m2-define-typography-level(16px, 28px, 400),
    $caption: mat.m2-define-typography-level(12px, 20px, 400),
    $button: mat.m2-define-typography-level(14px, 14px, 500),
);

$webclient-theme: mat.m2-define-light-theme((
  color: (
    primary: $webclient-primary,
    accent: $webclient-secondary,
    warn: $webclient-warn,
  ),
  typography: $global-typography
));

@include mat.all-component-themes($webclient-theme);