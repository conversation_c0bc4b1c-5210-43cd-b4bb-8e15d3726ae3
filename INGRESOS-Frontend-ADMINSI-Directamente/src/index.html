<!doctype html>
<html lang="es">

<head>
  <base href="/">
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
  <meta name="description" content="CoppelIO" />
  <meta name="keyword" content="" />
  <link rel="shortcut icon" href="./assets/img/logo-sym.png" />
  <title>Sistema de Ingresos Gestor</title>
  <style>
    @font-face {
      font-family: 'Avenir';
      src: url('assets/fonts/avenir/book/AvenirLTStd-Book.otf');
    }

    @font-face {
      font-family: 'Avenir Bold';
      src: url('assets/fonts/avenir/black/AvenirLTStd-Black.otf');
    }
  </style>
  <style id="antiClickjack">
    body { display: none !important; }
  </style>
  <script type="text/javascript">
    if (self === top) {
      const antiClickjack = document.getElementById('antiClickjack');
      antiClickjack.parentNode.removeChild(antiClickjack);
    }
  </script>
  <script>window.__theme = 'bs4';</script>
</head>
<!-- BODY options, add following classes to body to change options
  
  // Header options
  1. '.header-fixed'					- Fixed Header
  
  // Brand options
  1. '.brand-minimized'       - Minimized brand (Only symbol)
  
  // Sidebar options
  1. '.sidebar-fixed'					- Fixed Sidebar
  2. '.sidebar-hidden'				- Hidden Sidebar
  3. '.sidebar-off-canvas'		- Off Canvas Sidebar
  4. '.sidebar-minimized'			- Minimized Sidebar (Only icons)
  5. '.sidebar-compact'			  - Compact Sidebar
  
  // Aside options
  1. '.aside-menu-fixed'			- Fixed Aside Menu
  2. '.aside-menu-hidden'			- Hidden Aside Menu
  3. '.aside-menu-off-canvas'	- Off Canvas Aside Menu
  
  // Breadcrumb options
  1. '.breadcrumb-fixed'			- Fixed Breadcrumb
  
  // Footer options
  1. '.footer-fixed'					- Fixed footer
  
  -->

<body>
  <app-root class="app header-fixed sidebar-fixed aside-menu-fixed aside-menu-hidden sidebar-hidden breadcrumb-fixed">
    <!-- Enable bootstrap 4 theme -->
    <script>
      window.__theme = 'bs4';
    </script>
    <!-- App Loading... -->
  </app-root>
  <app-redirect></app-redirect> <!--  Selector for additional bootstrapped component (MSAL) -->
</body>

</html>