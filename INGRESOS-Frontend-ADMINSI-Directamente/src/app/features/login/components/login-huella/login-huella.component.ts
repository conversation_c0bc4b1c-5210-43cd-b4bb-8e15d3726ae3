import { Component, EventEmitter, Output } from '@angular/core';
import { NgForm } from '@angular/forms';

import { HuellaService } from '@oc/ngx-huella';
import { faArrowLeft } from '@fortawesome/free-solid-svg-icons';
import { environment } from 'src/environments/environment';

@Component({
  selector: 'app-login-huella',
  templateUrl: './login-huella.component.html',
  styles: [
    `.btn-primary {
      border-radius: 4px;
      border: 1px solid #E1E7ED;
      background: #31346B;
      padding: 12px;
    }`
  ]
})
export class LoginHuellaComponent {
  @Output() iniciarSesion = new EventEmitter<any>();
  @Output() regresar = new EventEmitter<any>();

  public backIcon = faArrowLeft;

  constructor(private huellaService: HuellaService) { }

  /**
   * Método que levanta el modulo para el biométrico
   */
  public login(f: NgForm) {
    if (!environment.production) {
      this.iniciarSesion.emit({ 
        user: f.form.value.numEmpleado,
        template: environment.huellaTemplate,
      });
      return;
    } 
    
    this.huellaService.getHuella().subscribe({
       next: (respuesta) => {
       if (respuesta.error !== '') { this.iniciarSesion.emit({ error: respuesta.error }) }
         else { this.iniciarSesion.emit({ user: f.form.value.numEmpleado, template: respuesta.template64 }) }
       },
       error: (error) => this.iniciarSesion.emit({ erro: error.message })
    });
  }
}
