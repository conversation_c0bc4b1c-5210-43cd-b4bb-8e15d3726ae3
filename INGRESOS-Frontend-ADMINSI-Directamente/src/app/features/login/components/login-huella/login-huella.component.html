<div class="px-2 py-2">
  <button class="btn" (click)="regresar.emit()">
    <fa-icon [icon]="backIcon" />
  </button>
</div>

<!--Formulario para validar colaborador-->
<form name="form" (ngSubmit)="f.form.valid && login(f)" #f="ngForm" novalidate class="px-4 py-5 fade-in">
  <h2 class="text-left">Inicia sesión con tu Huella Digital<span>.</span></h2>
  <p class="text-muted">Los mismos datos que utilizas en Coppel.com</p>
  <div class="form-group">
    <label>Número de empleado</label>
    <input
      [ngClass]="{ 'is-invalid': f.submitted && !empleado.valid }"
      name="numEmpleado"
      maxlength="8"
      minlength="8"
      class="form-control huella"
      #empleado="ngModel"
      appRestringirTipo
      ngModel
      required
    />
  </div>
  <div class="d-flex justify-content-center mt-4">
    <button class="btn btn-primary" [disabled]="f.form.invalid">Ingresar</button>
  </div>
</form>
