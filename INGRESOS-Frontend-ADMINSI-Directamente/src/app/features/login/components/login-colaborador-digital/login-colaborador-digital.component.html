<div class="px-2 py-2">
  <button class="btn" (click)="regresar.emit()">
    <fa-icon [icon]="backIcon" />
  </button>
</div>

<form name="form" (ngSubmit)="f.form.valid && login(f)" #f="ngForm" novalidate class="form2 fade-in px-4 py-2">
  <h2 class="text-left">Inicia sesión con tu Colaborador Digital<span>.</span></h2>
  <p class="text-muted">Los mismos datos que utilizas en Coppel.com</p>
  <div class="form-group">
    <label class="colaborador">Número de Colaborador o Correo</label>
    <input [ngClass]="{ 'is-invalid': f.submitted && !username.valid }" type="text" name="logonId"
      class="form-control usuario" #username="ngModel" ngModel required />
  </div>
  <div class="form-group mt-1">
    <label class="contrase">Contraseña</label>
    <input
      [ngClass]="{ 'is-invalid': f.submitted && !password.valid }"
      type="password"
      name="logonPassword"
      class="form-control"
      #password="ngModel"
      ngModel 
      required
    />
  </div>
  <div class="d-flex justify-content-center mt-4">
    <button class="btn btn-primary" type="submit" [disabled]="f.form.invalid"
      appCaptchaReader>Ingresar</button>
  </div>
</form>