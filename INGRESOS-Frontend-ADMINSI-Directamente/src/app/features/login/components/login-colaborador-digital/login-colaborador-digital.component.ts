import { Component, EventEmitter, Output } from '@angular/core';
import { NgForm } from '@angular/forms';
import { faArrowLeft, faEye } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-login-colaborador-digital',
  templateUrl: './login-colaborador-digital.component.html',
  styles: [
    `.btn-primary {
      border-radius: 4px;
      border: 1px solid #E1E7ED;
      background: #31346B;
      padding: 12px;
    }`
  ]
})
export class LoginColaboradorDigitalComponent {  
  @Output() regresar = new EventEmitter<void>();
  @Output() iniciarSesion = new EventEmitter<any>();

  public back() { this.regresar.emit() }
  
  /**
   * Método que emite el evento cuando el formulario se registra
   */
  public login(f: NgForm) { 
    this.iniciarSesion.emit(f.form.value) 
  }

  public backIcon = faArrowLeft;
  public eyeIcon = faEye;
}
