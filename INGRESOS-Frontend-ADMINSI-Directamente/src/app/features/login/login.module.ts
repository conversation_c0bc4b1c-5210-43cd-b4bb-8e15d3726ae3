import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

import { RecaptchaV3Module } from '@app-recaptcha';
import { LoginComponent } from './pages/login/login.component';
import { AadService } from 'src/app/services/idps/aad.service';
import { SsoService } from 'src/app/services/idps/sso.service';
import { AuthenticationService } from 'src/app/services/authentication.service';
import { CustomService } from 'src/app/services/idps/custom.service';
import { DirectivesModule } from 'src/app/directives/directives.module';

const APP_ROUTES: Routes = [
  {
    path: '',
    component: LoginComponent,
    data: {
      title: 'Login Page',
    },
  },
];

@NgModule({
  imports: [
    CommonModule,
    FormsModule,
    RecaptchaV3Module,
    RouterModule.forChild(APP_ROUTES),
    ReactiveFormsModule,
    FontAwesomeModule,
    DirectivesModule,
  ],
  declarations: [
    LoginComponent,
  ],
  providers: [AadService, SsoService, CustomService, AuthenticationService],
})
export class LoginModule {}
