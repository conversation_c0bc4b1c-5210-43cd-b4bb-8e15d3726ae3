<div *ngIf="obtenerEmpleadoSeleccionado() | async as empleado else elseBlock" class="container">
  <div class="container-usuario">
    <div class="d-flex justify-content-between align-items-start">
      <h3>
        {{empleado.nombre}} 
      </h3>
      <button *ngIf="busqueda" (click)="abrirModal()" class="btn btn-primary d-flex" style="gap: 0.8rem">
        <fa-icon icon="user" />
        Buscar
      </button>
    </div>
    <div class="usuario-info">
      <article>
        <span>Puesto</span>
        <label>{{empleado.puesto}}</label>
      </article>
      <article>
        <span>Teléfono</span>
        <label>{{empleado.telefono || 'N / A'}}</label>
      </article>
    </div>
  </div>
</div>

<ng-template #elseBlock>
  <div class="container">
    <div class="d-flex justify-content-between align-items-start">
      <h3>
        Seleccione usuario 
      </h3>
      <button *ngIf="busqueda" (click)="abrirModal()" class="btn btn-primary d-flex" style="gap: 0.8rem">
        <fa-icon icon="user" />
        Buscar
      </button>
    </div>
  </div>
</ng-template>

<!--Modal-->
<ng-template #modal>
  <app-modal-ayuda-usuario (seleccion)="seleccionarEmpleado($event)" />
</ng-template>