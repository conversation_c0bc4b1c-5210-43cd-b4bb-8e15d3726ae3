$backgroud-color: #EAF0F6;

.container-usuario {
  border-radius: 6px;
  background-color: $backgroud-color;

  h3 {
    font-size: 26px;
    font-style: normal;
    font-weight: 500;
    color: #33404E !important;
    max-width: 60%;
    text-overflow: ellipsis;
  }

  .usuario-info {
    display: flex;
    align-items: center;
    gap: 24px;
    padding-top: 40px;

    article {
      display: flex;
      flex-direction: column;
      gap: 8px;
      justify-content: flex-start;

      span:first-child {
        color: #4B5D6E !important;
        font-size: 14px;
      }

      label {
        font-size: 14px;
      }
    }
  }
}

.container {
  border-radius: 6px;
  background-color: $backgroud-color;
  min-height: 240px;
  min-width: 560px;
  padding: 50px 30px 0px 30px;

  h3 {
    font-size: 26px;
    font-style: normal;
    font-weight: 500;
    color: #B8BEC5;
  }
}
