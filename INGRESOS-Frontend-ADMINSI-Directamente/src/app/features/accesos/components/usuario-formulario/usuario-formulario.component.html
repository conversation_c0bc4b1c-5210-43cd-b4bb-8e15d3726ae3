<div class="root p-4 w-full position-relative">
  <div class="d-flex justify-content-between align-items-start" style="gap: 12px">
    <h5 class="font-weight-bold">Alta de acceso de usuario</h5>
    <button (click)="cerrarFormulario()" class="btn btn-outline-dark">
      Cerrar
    </button>
  </div>

  <div>
    <app-ayuda-usuario 
      [busqueda]="mostrarBotonBusqueda$ | async"
      [usuarioMonitor]="usuarioMonitor$ | async" 
      (seleccion)="empleadoSeleccionado$.next($event); errorUsuarioRequerido$.next(false)" 
    />
    <div *ngIf="errorUsuarioRequerido$ | async" class="invalid-feedback d-block">
      El empleado es requerido
    </div>
  </div>

  <form 
    (ngSubmit)="guardarFormulario()" 
    [formGroup]="formulario" 
    class="d-flex flex-column position-relative"
    style="gap: 1rem">
    <div class="d-flex w-100" style="gap: 1rem">
      <div class="form-group w-50">
        <label for="nombre">Perfil*</label>
        <select formControlName="idu_si_rol" class="form-select"
          [ngClass]="{ 'is-invalid': formulario.get('idu_si_rol')?.touched && formulario.get('idu_si_rol')?.invalid }">
          <option *ngFor="let rol of roles$ | async" [value]="rol.idu_si_rol">
            {{rol.nom_rol}}
          </option>
        </select>
        <div *ngIf="formulario.get('idu_si_rol')?.touched && formulario.get('idu_si_rol')?.invalid"
          class="invalid-feedback">
          El campo es requerido
        </div>
      </div>
      <div class="form-group w-50">
        <label for="opc_activo">Estatus*</label>
        <select formControlName="opc_activo" class="form-select">
          <option value="true">Activo</option>
          <option value="false">Inactivo</option>
        </select>
      </div>
    </div>
    <ng-container *ngIf="modulosRolSeleccionado$ | async as moduloRolSeleccionado">
      <div class="row">
        <div class="col-12">
          <label>Módulos con acceso</label>
        </div>
        <div class="col-12 mb-4 overflow-auto" style="max-height: 220px;">
          <app-chips-modulos 
            [modulos]="moduloRolSeleccionado"
          />
        </div>
      </div>
    </ng-container>
    <div class="btns mb-4">
      <button type="submit" class="btn btn-primary">
        Guardar
      </button>
      <button *ngIf="!(usuarioMonitor$ | async)" (click)="cancelar()" type="button" class="btn btn-outline-dark">
        Cancelar
      </button>
    </div>
  </form>
</div>
