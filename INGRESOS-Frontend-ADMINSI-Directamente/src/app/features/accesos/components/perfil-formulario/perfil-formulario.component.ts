import { Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { FormBuilder, Validators } from '@angular/forms';

import { Rol } from '@app-interfaces/rol.interface';

@Component({
  selector: 'app-perfil-formulario',
  templateUrl: './perfil-formulario.component.html',
  styles: [
  ]
})
export class PerfilFormularioComponent {
  @Input() set perfil(perfil: Rol) {
    this.formulario.patchValue(perfil);
    if (perfil?.idu_si_rol) this.titulo = 'Editar perfil'
  }

  @Output() guardar = new EventEmitter<Rol>();
  @Output() regresar = new EventEmitter<boolean>();

  private fb = inject(FormBuilder);
  
  public formulario = this.fb.group({
    idu_si_rol: [],
    nom_rol: ['', Validators.required],
    des_rol: ['', Validators.required],
    opc_activo: [true, Validators.required],
  });

  public titulo = 'Nuevo perfil'

  /**
   * Método que emite el evento al dar click guardar
   * en el formulario
   */
  public guardarFormulario() {
    if (this.formulario.invalid) {
      this.formulario.markAllAsTouched();
      return;
    }

    const perfil = this.formulario.value as Rol;
    this.guardar.emit(perfil);
  }
}
