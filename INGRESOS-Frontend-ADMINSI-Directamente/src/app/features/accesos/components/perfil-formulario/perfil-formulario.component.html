<form [formGroup]="formulario" (ngSubmit)="guardarFormulario()">
  <div class="row gy-2">
    <div class="col-8">
      <h5 class="font-weight-bold">{{titulo}}</h5>
    </div>
    <div class="col-4">
      <div class="d-flex justify-content-end" style="gap: 8px">
        <button type="submit" class="btn btn-primary">
          Guardar
        </button>
        <button (click)="regresar.emit(formulario.touched)" type="button" class="btn btn-outline-dark">
          Cancelar
        </button>
      </div>
    </div>
  </div>
  <div class="row">
    <div class="col-4">
      <div class="form-group">
        <label for="nombre">Nombre del perfil*</label>
        <input formControlName="nom_rol" type="text" class="form-control" permitir="letrasYNumeros" appRestringirTipo
          [ngClass]="{ 'is-invalid': formulario.get('nom_rol')?.touched && formulario.get('nom_rol')?.invalid }" />
        <div *ngIf="formulario.get('nom_rol')?.touched && formulario.get('nom_rol')?.invalid" class="invalid-feedback">
          El campo es requerido
        </div>
      </div>
    </div>
    <div class="col-4">
      <div class="form-group">
        <label for="des_rol">Descripción</label>
        <input formControlName="des_rol" type="text" class="form-control" permitir="letrasYNumeros" appRestringirTipo
          [ngClass]="{ 'is-invalid': formulario.get('des_rol')?.touched && formulario.get('des_rol')?.invalid }" />
        <div *ngIf="formulario.get('des_rol')?.touched && formulario.get('des_rol')?.invalid" class="invalid-feedback">
          El campo es requerido
        </div>
      </div>
    </div>
    <div class="col-4">
      <div class="form-group">
        <label for="opc_activo">Estatus*</label>
        <select formControlName="opc_activo" class="form-select">
          <option value="true">Activo</option>
          <option value="false">Inactivo</option>
        </select>
      </div>
    </div>
  </div>
</form>