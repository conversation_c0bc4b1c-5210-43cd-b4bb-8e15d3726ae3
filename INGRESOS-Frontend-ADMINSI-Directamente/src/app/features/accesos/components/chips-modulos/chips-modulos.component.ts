import { Component, Input } from '@angular/core';

import { <PERSON><PERSON><PERSON> } from 'src/app/interfaces/modulo.interface';

@Component({
  selector: 'app-chips-modulos',
  template: `
    <div class="fila">
      <ng-container
        *ngFor="let modulo of modulos"
      >
        <div class="chip fade-in">
          <div class="icon"><fa-icon icon="glasses"  /></div>
          <div class="icon" *ngIf="!modulo.opc_solo_lectura"><fa-icon icon="pencil" /></div>
          <div class="p-0 m-0" [innerHTML]=" modulo.nom_modulo"></div>
        </div>
      </ng-container>
    </div>
  `,
  styleUrls: ['./chips-modulos.component.scss'],
})
export class ChipsModulosComponent {
  @Input() modulos: Modulo[] = [];

}
