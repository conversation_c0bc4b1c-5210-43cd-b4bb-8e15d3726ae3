<app-tabla [mostrarMensajeVacio]="false" [seleccionar]="true">
  <thead>
    <tr>
      <th *ngFor="let cabecero of cabeceros; let i = index;">
        <div class="d-flex align-items-center">
          <div class="d-flex align-items-center" *ngIf="esSeleccionarNivelVisible(i)" style="gap: 8px; translate: -3px;">
            <label class="icon-checkbox m-0">
              <input 
                type="checkbox" 
                #chkLectura
                [checked]="estaNivelCompleto(i)"  
                (change)="seleccionarTodoNivel(chkLectura.checked, i)"
              >
              <span class="checkbox-icon-wrapper">
                <fa-icon class="checkbox-icon" icon="glasses" style="color: white; width: 14px;" />
              </span>
            </label>
  
            <label class="icon-checkbox m-0">
              <input 
                type="checkbox" 
                #chkEscritura
                [checked]="estaNivelCompleto(i, true)"  
                (change)="seleccionarTodoNivel(chkEscritura.checked, i, true)"
              >
              <span class="checkbox-icon-wrapper">
                <fa-icon class="checkbox-icon" icon="pencil" style="color: white; width: 14px;" />
              </span>
            </label>  
          </div>

          <div class="cabecero">
            {{cabecero.label}}
          </div>
        </div>
      </th>
    </tr>
  </thead>
  <tbody>
    <tr class="modulos">
      <td>
        <div class="d-flex flex-column">
          <ng-template 
            *ngFor="let modulo of modulosRaiz$ | async"
            [ngTemplateOutlet]="control" 
            [ngTemplateOutletContext]="{ $implicit: { modulo, nivel: 0 } }" 
          />
        </div>
      </td> 
      <ng-container *ngIf="modulosSeleccionados$ | async as modulosSeleccionados">
        <td *ngFor="let nivel of niveles">
          <div class="overflow-auto" style="max-height: 450px; height: 100%; max-width: 100%">
            <ng-template
              *ngFor="let modulo of obtenerHijos(modulosSeleccionados[nivel - 1])"
              [ngTemplateOutlet]="control"
              [ngTemplateOutletContext]="{ $implicit: { modulo, nivel } }"
            />
          </div>
        </td>
      </ng-container>
    </tr>
  </tbody>
</app-tabla>

<ng-template #control let-context>
  <button 
    class="controls list-group-item list-group-item-action" 
    (click)="agregarModuloSeleccionado(context.modulo, context.nivel)"
    [ngClass]="{ 'selected': estaModuloSeleccionado(context.modulo.idu_si_modulo) }"
  >
      <div class="d-flex align-items-center" style="gap: 8px; min-width: 50px;">
        <label *ngIf="context.modulo.submodulos.length else elseBlock" class="icon-checkbox">
          <input 
            type="checkbox" 
            [formControl]="obtenerControl(context.modulo.idu_si_modulo, 'lectura')"
            (change)="chequearPadre($event, context.modulo)"  
          >
          <span class="checkbox-icon-wrapper">
            <fa-icon class="checkbox-icon" icon="check" style="color: white" />
          </span>
        </label>
        <ng-template #elseBlock>
          <label class="icon-checkbox">
            <input 
              type="checkbox" 
              [formControl]="obtenerControl(context.modulo.idu_si_modulo, 'lectura')"
              (change)="chequearPermisoLectura($event, context.modulo)"  
            >
            <span class="checkbox-icon-wrapper">
              <fa-icon class="checkbox-icon" icon="glasses" style="color: white; width: 14px;"  />
            </span>
          </label>
          <label class="icon-checkbox">
            <input 
              type="checkbox" 
              [formControl]="obtenerControl(context.modulo.idu_si_modulo, 'escritura')" 
              (change)="chequearPermisoEscritura($event, context.modulo)"
            >
            <span class="checkbox-icon-wrapper">
              <fa-icon class="checkbox-icon" icon="pencil" style="color: white; width: 14px;" />
            </span>
          </label>
        </ng-template>
      </div>
      
      <div class="modulo-label" [innerHTML]="context.modulo.nom_modulo"></div>
  </button>
</ng-template>
