import {
  ChangeDetectionStrategy,
  Component,
  Input,
  OnInit,
  inject,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';

import { BehaviorSubject, Observable,map, tap } from 'rxjs';

import { <PERSON><PERSON><PERSON> } from '@app-interfaces/modulo.interface';
import { TablaCabeceros } from '@app-interfaces/tabla.interface';


type FormularioModulo = { lectura: boolean; escritura?: boolean };

@Component({
  selector: 'app-selector-permisos',
  templateUrl: './selector-permisos.component.html',
  styleUrls: ['./selector-permisos.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SelectorPermisosComponent implements OnInit  {
  @Input() set modulosSeleccionados(value: Modulo[]) {
    for (const { idu_si_modulo, opc_solo_lectura } of value || []) {
      const grupoFormulario = this.grupoFormularioPorIdu[idu_si_modulo];
      grupoFormulario.patchValue({
        lectura: true,
        escritura: !opc_solo_lectura
      })
    }
  }

  @Input() set modulos(value: Modulo[]) {
    this.modulos$.next(value);
    this.crearModulosPorIdu(value);
    this.crearFormulario(value);
  }

  private fb = inject(FormBuilder);
  
  public formularios = this.fb.array<FormGroup>([]);

  private grupoFormularioPorIdu: { [key: number]: FormGroup } = {};
  private modulosPorIdu: { [key: number]: Modulo } = {};

  public modulos$ = new BehaviorSubject<Modulo[]>([]);

  private _modulosRaiz: Modulo[];
  public modulosRaiz$: Observable<Modulo[]>;

  public modulosSeleccionados$ = new BehaviorSubject<Modulo[]>([]);

  public nivelesSubMenu: number = 3;

  public get cabeceros(): TablaCabeceros[] {
    const cabeceros: TablaCabeceros[] = [
      {
        label: 'Menú'
      }
    ];

    for (let i = 0; i < this.nivelesSubMenu; i += 1) {
      cabeceros.push({
        label: 'Sub-módulo'
      })
    }

    return cabeceros
  }

  public get niveles(): number[] {
    const niveles = [...Array(this.nivelesSubMenu).keys()].map((i) => i + 1);
    return niveles
  }

  ngOnInit(): void {
    this.modulosRaiz$ = this.modulos$.pipe(
      map((modulos) => modulos.filter(({ idu_si_modulo_padre }) => !idu_si_modulo_padre)),
      tap((modulosRaiz) => this._modulosRaiz = modulosRaiz)
    );
  }

  /** Método que retorna todos los módulos que fueron seleccionados */
  obtenerSeleccion() {
    const seleccion = [];

    for (const formulario of this.formularios.controls) {
      const { idu_si_modulo, lectura, escritura } = formulario.getRawValue();
      
      if (!lectura) continue
      
      seleccion.push({
        idu_si_modulo,
        opc_solo_lectura: !escritura,
      });
    } 

    return seleccion;
  }

  /** Método que retorna si un módulo esta seleccionado */
  estaModuloSeleccionado(iduModulo: number) {
    return this.modulosSeleccionados$
      .getValue()
      .findIndex(({ idu_si_modulo }) => idu_si_modulo === iduModulo) !== -1
  }

  /** Método que recorre los módulos y los almacena en un objeto por su idu  */
  crearModulosPorIdu(modulos: Modulo[]) {
    const modulosPorIdu: { [key: string]: Modulo } = {};
    
    const recorrerMenu = (modulo: Modulo) => {
      modulosPorIdu[modulo.idu_si_modulo] = modulo;
      for (const hijo of modulo.submodulos) {
        recorrerMenu(hijo)
      }
    }
    
    for (const raiz of modulos) {
      recorrerMenu(raiz)
    }
    
    this.modulosPorIdu = modulosPorIdu;
  }

  /** Método que crea el formArray */
  crearFormulario(raiz: Modulo[]) {    
    const recorrerMenu = (modulo: Modulo) => {
      const { idu_si_modulo, idu_si_modulo_padre } = modulo
      const grupo = this.fb.group({
        idu_si_modulo,
        idu_si_modulo_padre,
        lectura: false,
        escritura: false,
        es_padre: !!modulo.submodulos.length
      });
      
      this.grupoFormularioPorIdu[idu_si_modulo] = grupo;
      this.formularios.push(grupo as FormGroup);
      
      for (const hijo of modulo.submodulos) {        
        recorrerMenu(hijo);
      }
    }

    for (const padre of raiz) {
      recorrerMenu(padre)
    }
  }

  /** Método para obtener un control del formulario de un módulo */
  obtenerControl(iduSiModulo: number, campo: string) {
    const grupo = this.grupoFormularioPorIdu[iduSiModulo];
    return grupo.get(campo) as FormControl;
  }

  /** Método que agrega un idu a los módulos seleccionados */
  agregarModuloSeleccionado(modulo: Modulo, nivel: number) {
    const seleccionados = [...this.modulosSeleccionados$.getValue()];
    
    if (seleccionados[nivel] === modulo) {
      return;
    }
    
    seleccionados[nivel] = modulo;
    this.modulosSeleccionados$.next(seleccionados.slice(0, nivel + 1));
  }

  /** Método que obtiene los hijos de un módulo */
  obtenerHijos(modulo?: Modulo) {
    if (!modulo) return [];
    return modulo.submodulos;
  }

  /** Método que reacciona al chequeo de un permiso de escritura */
  chequearPermisoEscritura(event: any, modulo: Modulo) {
    let chequeado = event;
    if (typeof event !== 'boolean') {
      chequeado = event.target.checked;
    }

    if (!chequeado) return;

    const { idu_si_modulo } = modulo;
    const grupo = this.grupoFormularioPorIdu[idu_si_modulo];
    const controlLectura = grupo.get('lectura');
    controlLectura.patchValue(true);

    this.propagarChequeoAPadres(modulo.idu_si_modulo_padre);
  }

  /** Método que reacciona al chequeo de un permiso de lectura */
  chequearPermisoLectura(event: any, modulo: Modulo) {
    let chequeado = event;
    if (typeof event !== 'boolean') {
      chequeado = event.target.checked;
    }

    const { 
      idu_si_modulo_padre: iduPadre, 
      idu_si_modulo: iduModulo 
    } = modulo;

    const formulario = this.grupoFormularioPorIdu[iduModulo]
    if (!chequeado) {
      formulario.patchValue({
        escritura: false,
      });
    }

    this.propagarChequeoAPadres(iduPadre)
  }

  /** Método que reacciona al chequeo de un módulo padre */
  chequearPadre(event: any, modulo: Modulo, incluirEscritura: boolean = true) {
    let chequeado = event;
    if (typeof event !== 'boolean') {
      chequeado = event.target.checked;
    }

    const recorrerMenu = (chequeado: boolean, modulo: Modulo) => {
      for (const hijo of modulo.submodulos) {
        const { idu_si_modulo, submodulos } = hijo;
        const formulario = this.grupoFormularioPorIdu[idu_si_modulo];
        
        if (incluirEscritura) {
          formulario.patchValue({
            lectura: chequeado,
            escritura: chequeado,
          });
        } else {
          const valorFormulario: FormularioModulo = { lectura: chequeado }; 
          if (!chequeado) {
            valorFormulario.escritura = false
          }
          formulario.patchValue(valorFormulario)
        }
  
        if (submodulos.length) {
          recorrerMenu(chequeado, hijo)
        }
      } 
    }

    recorrerMenu(chequeado, modulo);

    this.propagarChequeoAPadres(modulo.idu_si_modulo_padre);
  }
  
  /** Método propaga los chequeos de módulos hijos hacia sus padres */
  propagarChequeoAPadres(idu?: number) {
    if (!idu) return 

    const modulo = this.modulosPorIdu[idu];
    const grupoFormulario = this.grupoFormularioPorIdu[idu]

    let estaSeleccionado = false;
    for (const { idu_si_modulo: iduHijo } of modulo.submodulos) {
      const grupo = this.grupoFormularioPorIdu[iduHijo];
      const { lectura } = grupo.getRawValue();
      if (lectura) {
        estaSeleccionado = true;
        break;
      }
    }

    grupoFormulario.patchValue({ lectura: estaSeleccionado });
    this.propagarChequeoAPadres(modulo.idu_si_modulo_padre);
  }

  /** Método que reacciona a la selección de un nivel */
  seleccionarTodoNivel(chequeado: boolean, nivel: number, incluirEscritura: boolean = false) {
    let hijos: Modulo[];
    if (nivel === 0) {
      hijos = this._modulosRaiz;
    } else {
      const padre = this.modulosSeleccionados$.getValue()[nivel - 1]
      hijos = padre.submodulos
    }

    for (const hijo of hijos) {
      const grupoFormulario = this.grupoFormularioPorIdu[hijo.idu_si_modulo]
      
      if (hijo.submodulos.length) {
        grupoFormulario.patchValue({ lectura: chequeado })
        this.chequearPadre(chequeado, hijo, incluirEscritura)
        continue;
      }

      if (incluirEscritura) {
        grupoFormulario.patchValue({ lectura: true, escritura: chequeado });
      } else {
        const valorFormulario: FormularioModulo = { lectura: chequeado };
        if (!chequeado) {
          valorFormulario.escritura = false;
        }
        grupoFormulario.patchValue(valorFormulario);
      }

      if (incluirEscritura) this.chequearPermisoEscritura(chequeado, hijo)
      else this.chequearPermisoLectura(chequeado, hijo)
    }
  }

  /** 
   * Método que verifica si el checkbox para seleccionar
   * un nivel es visible
   */
  esSeleccionarNivelVisible(nivel: number) {
    if (nivel === 0) return true;
    
    const modulosSeleccionados = this.modulosSeleccionados$.getValue();
    if (nivel > modulosSeleccionados.length) return false
    
    const hijos = modulosSeleccionados[nivel - 1].submodulos
    return !!hijos.length;
  }

  /** Método que verifica si un nivel está completamente seleccionado */
  estaNivelCompleto(nivel: number, incluirEscritura: boolean = false) {
    let hijos: Modulo[];
    if (nivel === 0) {
      hijos = this._modulosRaiz;
    } else {
      const padre = this.modulosSeleccionados$.getValue()[nivel - 1]
      hijos = padre.submodulos
    }

    for (const { idu_si_modulo } of hijos) {
      const grupoFormulario = this.grupoFormularioPorIdu[idu_si_modulo]
      const { lectura, escritura, es_padre } = grupoFormulario.getRawValue();
      if (!lectura) return false
      if (incluirEscritura && !es_padre && !escritura) return false;
    }

    return true
  }

}
