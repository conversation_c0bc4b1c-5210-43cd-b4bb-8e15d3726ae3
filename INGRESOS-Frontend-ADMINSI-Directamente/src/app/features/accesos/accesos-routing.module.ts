import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { UsuariosComponent } from './pages/usuarios/usuarios.component';
import { PerfilesComponent } from './pages/perfiles/perfiles.component';
import { PerfilComponent } from './pages/perfil/perfil.component';

const routes: Routes = [
  {
    path: 'usuarios',
    component: UsuariosComponent,
    data: { title: 'Usuarios' },
  },
  {
    path: 'perfiles',
    data: { title: 'Perfiles' },
    children: [
      {
        path: '',
        component: PerfilesComponent,
      },
      {
        path: 'agregar',
        component: PerfilComponent,
      },
      {
        path: ':idu_perfil',
        component: PerfilComponent,
      },
    ]
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AccesosRoutingModule {}
