import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

import { BsDropdownModule } from 'ngx-bootstrap/dropdown';

import { AccesosRoutingModule } from './accesos-routing.module';
import { UsuariosComponent } from './pages/usuarios/usuarios.component';
import { PerfilesComponent } from './pages/perfiles/perfiles.component';
import { ToasterModule } from 'src/app/shared/toaster/toaster.module';
import { ListadoComponent } from 'src/app/containers/listado/listado.component';
import { PerfilComponent } from './pages/perfil/perfil.component';
import { PerfilFormularioComponent } from './components/perfil-formulario/perfil-formulario.component';
import { SelectorPermisosComponent } from './components/selector-permisos/selector-permisos.component';
import { DirectivesModule } from 'src/app/directives/directives.module';
import { DrawerComponent } from 'src/app/shared/components/drawer/drawer.component';
import { UsuarioFormularioComponent } from './components/usuario-formulario/usuario-formulario.component';
import { AyudaUsuarioComponent } from './components/ayuda-usuario/ayuda-usuario.component';
import { ModalContainerComponent } from 'src/app/shared/components/modal-container/modal-container.component';
import { TablaComponent } from 'src/app/shared/components/tabla/tabla.component';
import { ChipsModulosComponent } from './components/chips-modulos/chips-modulos.component';
import { ModalAyudaUsuarioComponent } from './components/modal-ayuda-usuario/modal-ayuda-usuario.component';
import { ArbolComponent } from '@app-components/arbol/arbol.component';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

@NgModule({
  declarations: [
    UsuariosComponent,
    PerfilesComponent,
    PerfilComponent,
    PerfilFormularioComponent,
    SelectorPermisosComponent,
    UsuarioFormularioComponent,
    AyudaUsuarioComponent,
    ChipsModulosComponent,
    ModalAyudaUsuarioComponent,
  ],
  imports: [
    CommonModule,
    AccesosRoutingModule,
    ToasterModule,
    ListadoComponent,
    FormsModule,
    ReactiveFormsModule,
    DirectivesModule,
    BsDropdownModule,
    DrawerComponent,
    ModalContainerComponent,
    TablaComponent,
    ArbolComponent,
    FontAwesomeModule,
  ]
})
export class AccesosModule { }
