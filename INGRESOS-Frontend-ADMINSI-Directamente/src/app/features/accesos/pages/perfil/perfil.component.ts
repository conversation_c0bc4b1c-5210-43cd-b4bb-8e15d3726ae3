import { Component, ViewChild, inject } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { BehaviorSubject, filter, map, of, switchMap } from 'rxjs';

import { RolService } from '../../services/rol.service';
import { Rol } from '@app-interfaces/rol.interface';
import { ToasterService } from 'src/app/shared/toaster/toaster.module';
import { EDITAR_PERFIL, MODULOS_REQUERIDOS, REGISTRO_PERFIL } from 'src/app/constants/mensajes';
import { Modulo } from 'src/app/interfaces/modulo.interface';
import { DialogoConfirmacionService } from 'src/app/services/dialogo-confirmacion.service';
import { SelectorPermisosComponent } from '../../components/selector-permisos/selector-permisos.component';

@Component({
  selector: 'app-perfil',
  template: `
    <div class="row gy-3">
      <app-perfil-formulario
        [perfil]="rol$ | async"
        (guardar)="registrarRol($event)"
        (regresar)="regresar($event)" 
      />
      <app-selector-permisos
        *ngIf="modulos$ | async as modulos"
        [modulos]="modulos"
        [modulosSeleccionados]="modulosSeleccionados$ | async"
      />
    </div>
  `,
})
export class PerfilComponent {
  @ViewChild(SelectorPermisosComponent) selectorPermisos: SelectorPermisosComponent;

  private router = inject(Router);
  private activatedRoute = inject(ActivatedRoute);
  private rolService = inject(RolService);
  private toastService = inject(ToasterService);
  private dialogoConfirmacioService = inject(DialogoConfirmacionService);

  public modulos$ = this.rolService.obtenerModulos();

  public procesosSeleccionados$ = new BehaviorSubject<number[]>([]);

  public rol$ = this.activatedRoute.params.pipe(
    switchMap(({ idu_perfil }) =>
      idu_perfil ? this.rolService.obtenerPorId(idu_perfil) : of(null)
    )
  );

  public modulosSeleccionados$ = this.rol$.pipe(
    filter((rol) => !!rol),
    map(({ modulos }) => modulos)
  )

  public modulosPerfil$ = this.rol$.pipe(
    filter((perfil) => !!perfil),
    map(({ modulos }) =>
      (modulos as Modulo[]).map(({ idu_si_modulo: idu_modulo }) => idu_modulo)
    )
  );

  /**
   * Función para el botón regresar del formulario
   */
  public async regresar(formularioSucio: boolean) {
    if (formularioSucio) {
      const confirmado = await this.dialogoConfirmacioService.mostrarDialogo();
      if (!confirmado) return;
    }

    this.router.navigateByUrl('/accesos/perfiles');
  }

  /**
   * Función para el botón guardar del formulario
   */
  public registrarRol(rol: Rol) {
    const modulos = this.selectorPermisos.obtenerSeleccion();

    if (!modulos.length) {
      this.toastService.showWarn(MODULOS_REQUERIDOS);
      return;
    }

    if (rol.idu_si_rol) {
      this.rolService
        .editarRol(rol.idu_si_rol, { ...rol, modulos })
        .subscribe({
          next: () => {
            this.toastService.showApproval(EDITAR_PERFIL);
            this.router.navigateByUrl('/accesos/perfiles');
          },
        });
      return;
    }

    delete rol.idu_si_rol;
    this.rolService.registrarRol({ ...rol, modulos }).subscribe({
      next: () => {
        this.toastService.showApproval(REGISTRO_PERFIL);
        this.router.navigateByUrl('/accesos/perfiles');
      },
    });
  }
}
