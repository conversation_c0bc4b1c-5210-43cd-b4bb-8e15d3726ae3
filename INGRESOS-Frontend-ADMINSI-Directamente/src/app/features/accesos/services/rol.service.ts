import { HttpClient } from '@angular/common/http';
import { Injectable, inject } from '@angular/core';

import { Modu<PERSON> } from '@app-interfaces/modulo.interface';
import { Paginado } from '@app-interfaces/paginado.interface';
import { Rol } from '@app-interfaces/rol.interface';
import {
  CATALOGOS,
  LISTA,
  MODULOS,
  PERFILES,
  endpoint,
} from '@app-constants/uris';

@Injectable({
  providedIn: 'root',
})
export class RolService {
  private http = inject(HttpClient);

  /**
   * Petición HTTP para obtener todos los módulos para los
   * permisos dentro del sistema
   */
  public obtenerModulos() {
    return this.http.get<Modulo[]>(endpoint(CATALOGOS, MODULOS));
  }

  /**
   * Petición HTTP para obtener listado de roles
   */
  public obtenerListado(
    pagina: number,
    registrosPorPagina: number,
    busqueda?: string
  ) {
    return this.http.get<Paginado<Rol>>(endpoint(PERFILES, LISTA), {
      params: {
        pagina,
        registrosPorPagina,
        busqueda,
      },
    });
  }

  /**
   * Petición para obtener todos los roles
   */
  public obtenerTodos() {
    return this.http.get<Rol[]>(endpoint(PERFILES));
  }

  /**
   * Petición para obtener un rol por idu_rol
   */
  public obtenerPorId(idu_rol: number) {
    return this.http.get<Rol>(endpoint(PERFILES, idu_rol));
  }

  /**
   * Petición HTTP para registrar un nuevo rol
   */
  public registrarRol(rol: Rol) {
    return this.http.post(endpoint(PERFILES), rol);
  }

  /**
   * Petición HTTP para editar un rol
   */
  public editarRol(idu_rol: number, rol: Rol) {
    return this.http.put(endpoint(PERFILES, idu_rol), rol);
  }

  /**
   * Petición HTTP para eliminar un rol
   */
  public eliminarRol(idu_rol: number) {
    return this.http.delete(endpoint(PERFILES, idu_rol));
  }
}
