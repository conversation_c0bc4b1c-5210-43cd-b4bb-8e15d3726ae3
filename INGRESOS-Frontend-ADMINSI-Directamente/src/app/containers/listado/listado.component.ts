import {
  ChangeDetectionStrategy,
  Component,
  EventEmitter,
  Input,
  Output,
  TemplateRef,
  ViewChild,
  inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';

import { faPlus } from '@fortawesome/free-solid-svg-icons';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

import { TablaCabeceros } from '@app-interfaces/tabla.interface';
import { TablaComponent } from 'src/app/shared/components/tabla/tabla.component';
import { Paginado } from '@app-interfaces/paginado.interface';
import { EstatusComponent } from '@app-components/estatus/estatus.component';
import { OpcionesTablaComponent } from '@app-components/opciones-tabla/opciones-tabla.component';
import { DialogoConfirmacionService } from '@app-dialogo-confirmacion';

@Component({
  selector: 'app-listado',
  templateUrl: './listado.component.html',
  imports: [
    CommonModule,
    TablaComponent,
    EstatusComponent,
    OpcionesTablaComponent,
    FontAwesomeModule,
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
})
export class ListadoComponent {
  @ViewChild('estatus', { static: true })
  estatusTemplate: TemplateRef<EstatusComponent>;
  @ViewChild('opciones', { static: true })
  opcionesTemplate: TemplateRef<OpcionesTablaComponent>;

  @Input() titulo: string = '';
  @Input() cabeceros: TablaCabeceros[] = [];
  @Input() paginado?: Paginado<any>;
  @Input() agregarDeshabilitado: boolean = false;
  @Input() mensajeVacio: string = 'Sin registros';

  @Output() agregar = new EventEmitter<void>();
  @Output() paginaCambio = new EventEmitter<number>();
  @Output() busqueda = new EventEmitter<string>();
  @Output() editar = new EventEmitter<any>();
  @Output() eliminar = new EventEmitter<any>();
  @Output() registrosPorPaginaCambio = new EventEmitter<number>();

  private dialogoConfirmacionService = inject(DialogoConfirmacionService);

  public iconoAgregar = faPlus;

  get numeroElemetos(): string {
    const total = this.paginado?.total || 0;
    return `${total} ${total === 1 ? 'Elemento' : 'Elementos'}`
  }

  /**
   * Propiedad para obtener los cabeceros de la tabla
   */
  get cabecerosTabla() {
    return <TablaCabeceros[]>[
      ...this.cabeceros,
      { label: 'Estatus', template: this.estatusTemplate },
      { label: 'Opciones', template: this.opcionesTemplate },
    ];
  }

  /**
   * Método que reacciona al evento de eliminar un
   * registro
   */
  public async eliminarRegistro(registro: any) {
    const confirmado = await this.dialogoConfirmacionService.mostrarDialogo();
    if (!confirmado) return;

    this.eliminar.emit(registro);
  }

}
