<ng-template #estatus let-registro>
  <app-estatus [estatus]="registro?.opc_activo" />
</ng-template>

<ng-template #opciones let-registro>
  <app-opciones-tabla (editar)="editar.emit(registro)" (eliminar)="eliminarRegistro(registro)" />
</ng-template>

<div class="row gy-3">
  <div class="col-12 col-sm-12 col-lg-7 col-xl-8 align-self-end">
    <div class="d-flex align-items-baseline gap-2">
      <h5 class="font-weight-bold m-0">{{titulo}}</h5>
      <p class="m-0 text-secondary form-text">{{numeroElemetos}}</p>
    </div>
    <div class="col-6 pl-0">
      <ng-content select="[filtros]" />
    </div>
  </div>
  <div class="col-12 col-sm-12 col-lg-5 col-xl-4 d-flex justify-content-end" style="gap: 1rem">
    <input type="text" class="form-control" placeholder="Buscar por nombre" #txtBusqueda
    (change)="busqueda.emit(txtBusqueda.value)">
    <button class="btn btn-primary d-flex align-items-center lh-1" style="gap: 0.5rem" (click)="agregar.emit()"
      [disabled]="agregarDeshabilitado">
      <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
        <circle cx="6.5" cy="6.5" r="6" stroke="white"/>
        <path d="M8.64286 5.96429H7.03571V4.35714C7.03571 4.15993 6.87578 4 6.67857 4H6.32143C6.12422 4 5.96429 4.15993 5.96429 4.35714V5.96429H4.35714C4.15993 5.96429 4 6.12422 4 6.32143V6.67857C4 6.87578 4.15993 7.03571 4.35714 7.03571H5.96429V8.64286C5.96429 8.84007 6.12422 9 6.32143 9H6.67857C6.87578 9 7.03571 8.84007 7.03571 8.64286V7.03571H8.64286C8.84007 7.03571 9 6.87578 9 6.67857V6.32143C9 6.12422 8.84007 5.96429 8.64286 5.96429Z" fill="white"/>
      </svg>        
      Agregar
    </button>
  </div>

  <div class="col-12 h-75">
    <app-tabla
      [cabeceros]="cabecerosTabla"
      [registros]="paginado?.registros"
      [total]="paginado?.total"
      [estatus]="false"
      [paginado]="true"
      [mensajeVacio]="mensajeVacio"
      (onPaginaChange)="paginaCambio.emit($event.page)"
      (onCantidadChange)="registrosPorPaginaCambio.emit($event)"
    />
  </div>
</div>