import {
  ChangeDetectionStrategy,
  Component,
  Input,
  On<PERSON><PERSON>roy,
  OnInit,
  inject,
  signal,
} from '@angular/core';
import { Router } from '@angular/router';

import { Subscription } from 'rxjs';

import { SidebarItem } from '@app-interfaces/sidebar.interface';
import { MenuService } from '@app-services/menu.service';

@Component({
  selector: 'app-sidebar-item',
  template: `
    <a
      [routerLink]="menuItem.url"
      [routerLinkActiveOptions]="{ exact: true }"
      routerLinkActive="selected"
      class="menu-item menu-link"
      (click)="click()"
      *ngIf="!menuItem.children?.length; else elseBlock"
    >
      <div class="d-flex" style="gap: 1.4rem">
        <fa-icon [icon]="menuItem.icon" />
        <span class="cursor-pointer" for="link-menu">
          {{ menuItem.name }}
        </span>
      </div>
    </a>
    <ng-template #elseBlock>
      <section
        [ngClass]="{ selected: estaSeleccionado() }"
        class="menu-item menu-link"
        (click)="click()"
      >
        <div class="d-flex" style="gap: 1.4rem">
          <fa-icon [icon]="menuItem.icon" />
          <span class="cursor-pointer" for="link-menu">
            {{ menuItem.name }}
          </span>
        </div>
      </section>
      <article [collapse]="hijosColapsados()" [isAnimated]="true">
        <div class="menu-options">
          <ng-container *ngFor="let submodulo of menuItem.children">
            <app-sidebar-link [menuItem]="submodulo" />
          </ng-container>
        </div>
      </article>
    </ng-template>
  `,
  styleUrls: ['./sidebar-item.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SidebarItemComponent implements OnInit, OnDestroy {
  @Input() menuItem: SidebarItem;
  @Input() openButton: boolean = false;

  private router = inject(Router);
  public menuService = inject(MenuService);

  public hijosColapsados = signal(false);
  public menuSub: Subscription;

  ngOnInit(): void {
    this.menuSub = this.menuService.isMenuOpen$.subscribe((open) => {
      if (!open) this.hijosColapsados.set(true);
    });
  }

  ngOnDestroy(): void {
    this.menuSub.unsubscribe();
  }

  public click() {
    const isOpen = this.menuService.isMenuOpen;
    const toggle = () => this.hijosColapsados.set(!this.hijosColapsados());
  
    if (!isOpen) {
      setTimeout(toggle, 200);
    } else {
      toggle();
    }
  
    this.menuService.abrirMenu();
  }

  public estaSeleccionado(): boolean {
    const { url } = this.menuItem;
    const rutaActiva = this.router.url;
    return rutaActiva.includes(url);
  }
}
