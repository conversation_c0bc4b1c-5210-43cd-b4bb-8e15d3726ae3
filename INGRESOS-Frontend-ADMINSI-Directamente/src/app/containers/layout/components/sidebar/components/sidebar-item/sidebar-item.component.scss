$border-color: #F2F2F2;
$selected-color: #F7F7F7;
$hover-color: #FAFAFA;
$menu-link-color: #1A3A53;

.menu-item {
    text-decoration: none;
    display: flex;
    align-items: baseline;
    justify-content: space-between;
    min-width: 240px;
    padding: 1rem;
    position: relative;
    transition: background-color 0.3s ease;
    cursor: pointer;

    &:hover {
        background-color: $hover-color;
    }
}

.menu-link {
    color: $menu-link-color;
}

.menu-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-left: 1.5rem;
    border-left: $border-color 2px solid;
    padding-left: 4px;
    padding-top: 1rem;
    margin-top: 0.5rem;
    padding-bottom: 1rem;
}

.selected {
    background-color: $selected-color;
}