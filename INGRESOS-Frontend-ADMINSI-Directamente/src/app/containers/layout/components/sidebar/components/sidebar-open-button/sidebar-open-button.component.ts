import { Component, inject } from '@angular/core';

import { faChevronLeft, faChevronRight } from '@fortawesome/free-solid-svg-icons';
import { map } from 'rxjs';

import { MenuService } from 'src/app/services/menu.service';

@Component({
  selector: 'app-sidebar-open-button',
  template: `
    <button class="btn open-btn" (click)="menuService.toggleMenu()">
      <fa-icon [icon]="icono$ | async" size="xs" />
    </button>
  `,
  styleUrls: ['./sidebar-open-button.component.scss']
})
export class SidebarOpenButtonComponent {
  public menuService = inject(MenuService);

  public icono$ = this.menuService.isMenuOpen$.pipe(
    map((isOpen) => isOpen ? faChevronLeft : faChevronRight)
  );
}
