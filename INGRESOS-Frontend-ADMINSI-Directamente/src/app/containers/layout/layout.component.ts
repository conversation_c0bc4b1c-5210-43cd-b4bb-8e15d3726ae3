import { AfterViewInit, ChangeDetectorRef, Component, ViewChild } from '@angular/core';
import { ActivatedRoute, RouterOutlet } from '@angular/router';

import { fadeAnimation } from '@app-shared/animations/fade-in';

@Component({
  selector: 'app-layout',
  
  template: `
    <div style="height: 100vh; overflow: hidden">
      <appbar />
      <main class="d-flex w-100 h-100 ">
        <app-sidebar />
        <div
          [@fadeAnimation]="animacion"
          class="h-100 container-fluid px-5 py-4 overflow-y-scroll overflow-x-hidden"
          style="max-height: 90%; overflow-y: auto" 
        >
          <router-outlet #outlet="outlet" />
        </div>
      </main>
    </div>
  `,
  animations: [
    fadeAnimation,
  ]
})
export class LayoutComponent implements AfterViewInit {
  @ViewChild(RouterOutlet, { static: true }) outlet: RouterOutlet;

  constructor(private cdr: ChangeDetectorRef) { }

  ngAfterViewInit() {
    this.cdr.detectChanges();
  }

  public get animacion(): ActivatedRoute | string {
    return this.outlet.isActivated ? this.outlet.activatedRoute : ''
  }
}
