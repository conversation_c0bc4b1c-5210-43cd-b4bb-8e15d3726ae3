import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatMenuModule } from '@angular/material/menu';
import { CollapseModule } from 'ngx-bootstrap/collapse';

import { AppbarComponent } from './components/appbar/appbar.component';
import { SidebarComponent } from './components/sidebar/sidebar.component';
import { LayoutComponent } from './layout.component';
import { SidebarItemComponent } from './components/sidebar/components/sidebar-item/sidebar-item.component';
import { SidebarLinkComponent } from './components/sidebar/components/sidebar-link/sidebar-link.component';
import { SidebarOpenButtonComponent } from './components/sidebar/components/sidebar-open-button/sidebar-open-button.component';
import { TooltipModule } from 'ngx-bootstrap/tooltip';


@NgModule({
  declarations: [
    AppbarComponent,
    SidebarComponent,
    LayoutComponent,
    SidebarItemComponent,
    SidebarLinkComponent,
    SidebarOpenButtonComponent,
  ],
  imports: [
    CommonModule,
    RouterModule,
    FontAwesomeModule,
    CollapseModule,
    TooltipModule,
    MatMenuModule,
  ],
})
export class LayoutModule { }
