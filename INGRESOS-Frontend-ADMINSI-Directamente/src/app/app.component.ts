import { Component } from '@angular/core';

import { FaIconLibrary } from '@fortawesome/angular-fontawesome';
import { far } from '@fortawesome/free-regular-svg-icons';
import { fas } from '@fortawesome/free-solid-svg-icons';
import { Settings } from 'luxon';

@Component({
  selector: 'app-root',
  template: `
    <app-dialogo-confirmacion />
    <router-outlet />
  `
})
export class AppComponent {
  public isIframe = false;

  constructor(iconos: FaIconLibrary) {
    iconos.addIconPacks(fas, far);
  }

  public ngOnInit() {
    this.isIframe = window !== window.parent && !window.opener;
    Settings.defaultLocale = 'es';
    Settings.defaultZone = 'America/Chihuahua';
  }

}
