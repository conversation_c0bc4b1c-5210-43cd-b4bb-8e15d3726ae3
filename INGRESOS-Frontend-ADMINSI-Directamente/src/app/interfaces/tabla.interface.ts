import type * as CSS from "csstype";
import { SafeHtml } from "@angular/platform-browser";
import { TemplateRef } from "@angular/core";

export interface TablaCabeceros<Type = any> {
  key?: keyof Type;
  label: string;
  transform?: (registro: Type) => string | SafeHtml;
  estilo?: (registro: Type) => CSS.Properties;
  clases?: (registro: Type) => { [key: string]: boolean };
  template?: TemplateRef<any>
  bold?: boolean;
}
