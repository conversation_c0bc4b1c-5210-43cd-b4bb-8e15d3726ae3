import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { MsalGuard } from '@azure/msal-angular';
import { firstValueFrom } from 'rxjs';

import { SecureStorageService } from '../services/secure-storage.service';
import { SessionService } from '../state/session.service';
import { ToasterService } from '@app-toaster';
import { AadService } from '../services/idps/aad.service';

@Injectable()
export class AuthGuard  {
  constructor(
    private session: SessionService,
    private storageService: SecureStorageService,
    private toasterService: ToasterService,
    private msalGuard: MsalGuard,
    private addService: AadService,
    private router: Router
  ) {}

  // - REGLAS DE AUTENTICACIÓN

  async canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot
  ): Promise<boolean | UrlTree> {
    const token = this.storageService.getItem('token');
    const idp = this.storageService.getItem('idp');

    if (idp === 'AAD') {
      const canActivate = await firstValueFrom(
        this.msalGuard.canActivate(route, state)
      );
      if (!canActivate) {
        return this.interrupt('La sesión expiró');
      }
    }

    if (!token) {
      this.router.navigate(['/login']);
      return false;
    }

    let autenticado = true;
    if (!this.session.getCurrentUser()) {
      autenticado = await firstValueFrom(this.session.autenticarUsuario());
      if (!autenticado) {
        return this.interrupt('La sesión expiró');
      }
    }

    return autenticado;
  }

  // - LÓGICA DE CIERRE DE SESIÓN

  private interrupt(error: any): boolean {
    if (typeof error === 'string') {
      this.toasterService.showWarn(error, 'Session Error');
    }
    this.session.logout();

    return false;
  }
}
