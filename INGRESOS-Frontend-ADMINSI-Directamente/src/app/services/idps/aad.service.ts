import { Inject, Injectable, inject } from '@angular/core';
import { Router } from '@angular/router';
import {
  MsalBroadcastService,
  MsalGuardConfiguration,
  MsalService,
  MSAL_GUARD_CONFIG,
} from '@azure/msal-angular';
import {
  EventMessage,
  EventType,
  InteractionStatus,
  RedirectRequest,
} from '@azure/msal-browser';
import { Observable, Subject } from 'rxjs';
import { filter, takeUntil } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { SecureStorageService } from '../secure-storage.service';
import { SessionService } from 'src/app/state/session.service';

const GRAPH_URL = environment.authIdp.AAD.graphUrl;
const GRAPH_PARAMS =
  'displayName,givenName,surname,employeeId,mail,jobTitle,department,companyName';
const GRAPH_ENDPOINT = `${GRAPH_URL}/v1.0/me?$select=${GRAPH_PARAMS}`;

@Injectable({
  providedIn: 'root',
})
export class AadService {
  private sesionService = inject(SessionService);

  private readonly _destroying$ = new Subject<void>();

  public readonly _loginSuccess$ = new Subject<string>();

  constructor(
    @Inject(MSAL_GUARD_CONFIG) private msalGuardConfig: MsalGuardConfiguration,
    private broadcastService: MsalBroadcastService,
    private authService: MsalService,
    private storageService: SecureStorageService,
    private router: Router
  ) {
    const idpSetted = typeof sessionStorage.getItem('idp') == 'string';
    const aadEnabled = environment.authMechanisms.azureAD;

    if (idpSetted && aadEnabled) {
      this.listenInteraction();
      this.listenAuth();
    }
  }

  private listenInteraction() {
    this.broadcastService.inProgress$
      .pipe(
        filter(
          (status: InteractionStatus) => status === InteractionStatus.None
        ),
        takeUntil(this._destroying$)
      )
      .subscribe({
        next: () => {
          this.checkAndSetActiveAccount();
        },
      });
  }

  private listenAuth() {
    this.broadcastService.msalSubject$
      .pipe(
        filter(
          (msg: EventMessage) => msg.eventType === EventType.LOGIN_SUCCESS
        ),
        takeUntil(this._destroying$)
      )
      .subscribe({
        next: (authentication: any) => {
          if (authentication.payload['accessToken']) {
            const token = authentication.payload['accessToken'];
            this._loginSuccess$.next(token);
            this.storageService.setItem('token', token);
            this.sesionService.autenticarUsuario().subscribe((autenticado) => {
              if (autenticado) this.router.navigate(['']);
              else {
                localStorage.clear();
                sessionStorage.clear();
              }
            });
          }
        },
      });
  }

  private checkAndSetActiveAccount() {
    let activeAccount = this.authService.instance.getActiveAccount();
    if (
      !activeAccount &&
      this.authService.instance.getAllAccounts().length > 0
    ) {
      let accounts = this.authService.instance.getAllAccounts();
      this.authService.instance.setActiveAccount(accounts[0]);
    }
  }

  public fetchProfile(): Observable<any> {
    return this.sesionService.autenticarUsuario();
  }

  public login() {
    if (this.msalGuardConfig.authRequest) {
      this.authService.loginRedirect({
        ...this.msalGuardConfig.authRequest,
      } as RedirectRequest);
    } else {
      this.authService.loginRedirect();
    }
  }
}
