import { Injectable } from '@angular/core';

import { BehaviorSubject, Subject, take } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class DialogoConfirmacionService {
  private _dialogoVisible = new BehaviorSubject<boolean>(false);
  private _dialogoRespuesta = new Subject<boolean>();

  public dialogoVisible$ = this._dialogoVisible.asObservable();

  /**
   * Función para responder el diálogo
   */
  public responderDialogo(confirmado: boolean) {
    this._dialogoRespuesta.next(confirmado);
    this._dialogoVisible.next(false);
  }

  /**
   * Función para mostrar un diálogo de confirmación
   */
  public mostrarDialogo(): Promise<boolean> {
    return new Promise((resolve) => {
      this._dialogoVisible.next(true);
      this._dialogoRespuesta
        .pipe(take(1))
        .subscribe((confirmado) => resolve(confirmado));
    });
  }
}
