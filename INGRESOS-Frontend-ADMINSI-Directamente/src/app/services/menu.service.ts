import { Injectable, inject } from '@angular/core';

import { BehaviorSubject, Observable, of } from 'rxjs';

import { SessionService } from '../state/session.service';
import { SIDEBAR_ITEMS } from '../constants/menu';
import { SidebarItem } from '../interfaces/sidebar.interface';

@Injectable({
  providedIn: 'root',
})
export class MenuService {
  private sesionService = inject(SessionService);

  private _isMenuOpen$ = new BehaviorSubject(true);
  public _currentMenu$ = new BehaviorSubject(SIDEBAR_ITEMS);

  public isMenuOpen$ = this._isMenuOpen$.asObservable();

  public readonly currentMenu$: Observable<SidebarItem[]> = of(SIDEBAR_ITEMS);

  public get isMenuOpen(): boolean {
    return this._isMenuOpen$.getValue();
  }

  public abrirMenu() {
    this._isMenuOpen$.next(true);
  }

  public cerrarMenu() {
    this._isMenuOpen$.next(false);
  }

  public toggleMenu() {
    this._isMenuOpen$.next(!this._isMenuOpen$.getValue());
  }

}
