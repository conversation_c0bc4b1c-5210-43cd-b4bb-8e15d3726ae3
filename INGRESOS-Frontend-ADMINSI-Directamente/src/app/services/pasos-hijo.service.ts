import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { PasosEnum } from '../enums/pasos.enum';
import { Paso } from '../interfaces/paso.interface';

@Injectable({
  providedIn: 'root'
})
export class StepperChildrenService {
  public pasos = new BehaviorSubject<Paso[]>([]);
  constructor() { }

  /* Metodo para mostrar el siguiente contenido del wizard */
  Siguiente(): void {
    const navTap = document.querySelectorAll('#wizard ol.nav li.nav-item.active');
    if (navTap.length > 0) {
      let siguiente = document.querySelectorAll('[data-tab="' + navTap[0].nextElementSibling.id + '"]')[0];
      let actual = siguiente.previousElementSibling;
      this.pasos.next(this.pasos.value.map((paso) => {
        if (paso.key == actual.id)
          paso.estatus = PasosEnum.Listo;
        if (paso.key == siguiente.id)
          paso.estatus = PasosEnum.Proceso;
        return paso;
      }));

      siguiente.classList.add('active');
      siguiente.setAttribute('data-opened-step', 'true');
      actual.classList.remove('active');
      actual.classList.add('done');
      this.DeshabilitarContenido();
      document.getElementById('contenido' + siguiente.id).setAttribute('data-opened-step', 'true');
    }
  }

  /**
   * La función Anterior se utiliza para navegar al paso anterior en una interfaz similar a un
   * asistente.
   */
  Anterior(): void {
    const navTap = document.querySelectorAll('#wizard ol.nav li.nav-item.active');
    if (navTap.length > 0) {
      let actual = document.querySelectorAll('[data-tab="' + navTap[0].previousElementSibling.id + '"]')[0];
      let siguiente = actual.nextElementSibling;
      actual.classList.add('active');
      actual.classList.remove('done');
      this.pasos.next(this.pasos.value.map(paso => {
        if (paso.key == actual.id)
          paso.estatus = PasosEnum.Proceso;
        if (paso.key == siguiente.id)
          paso.estatus = PasosEnum.Pendiente;
        return paso;
      }));

      siguiente.classList.remove('active');
      siguiente.classList.remove('done');
      this.DeshabilitarContenido();
      document.getElementById('contenido' + actual.id).setAttribute('data-opened-step', 'true');
    }
  }

  /* Metodo para deshabilitar todos los contenidos del wizard */
  DeshabilitarContenido(): void {
    let stepContent = document.querySelectorAll('[data-opened-step]');
    for (let i = 0; i < stepContent.length; i++) {
      stepContent[i].setAttribute('data-opened-step', 'false');
    }
  }
}
