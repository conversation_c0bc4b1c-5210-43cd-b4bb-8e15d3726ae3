import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class DrawerService {
  private _isOpen = new BehaviorSubject(false);

  public isOpen = this._isOpen.asObservable();

  /**
   * Función para mostrar un drawer
   */
  mostrarDrawer() {
    this._isOpen.next(true);
  }

  /**
   * Función para cerrar un drawer
   */
  cerrarDrawer() {
    this._isOpen.next(false);
  }
}
