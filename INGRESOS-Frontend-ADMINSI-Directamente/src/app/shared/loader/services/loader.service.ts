import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class LoaderService {

  private fullContainer;
  private overlayElement;
  private loaderElement;
  
  private _contador: number = 0;
  private _isLoading = new BehaviorSubject<boolean>(false);

  public isLoading = this._isLoading.asObservable();

  constructor() {
    this.fullContainer = document.body;
    this.overlayElement = this.createOverlay()
    this.overlayElement.style.position = 'fixed';
    this.loaderElement = this.createLoader()
    this.overlayElement.appendChild(this.loaderElement);
    this.fullContainer.appendChild(this.overlayElement);
  }

  /**
   * createOverlay
   */
  public createOverlay(full = false) {
    const overlayElement = document.createElement('div');
    overlayElement.style.position = full ? 'fixed' : 'absolute';
    overlayElement.style.display = 'none';
    overlayElement.style.justifyContent = 'center';
    overlayElement.style.alignItems = 'center';
    overlayElement.style.top = '0';
    overlayElement.style.left = '0';
    overlayElement.style.width = '100%';
    overlayElement.style.height = '100%';
    overlayElement.style.zIndex = '9999';
    overlayElement.style.backgroundColor = 'rgb(255, 255, 255, 0.5)';
    return overlayElement;
  }
  /**
   * createLoader
   */
  public createLoader() {
    const loaderElement = document.createElement('div');
    loaderElement.setAttribute('role', 'status');
    loaderElement.classList.add('spinner-border');
    loaderElement.classList.add('text-primary');
    loaderElement.style.width = '12vh';
    loaderElement.style.height = '12vh';
    return loaderElement;
  }

  /**
   * setVisibleState
   */
  public setVisibleState(
    show: boolean,
    overlayElement: HTMLElement = this.overlayElement
  ) {
    if (show) {
      this._contador += 1;
      overlayElement.style.display = 'flex';
      this._isLoading.next(true);
    } else {
      if (this._contador > 0) this._contador -= 1;
      if (!this._contador) {
        overlayElement.style.display = 'none';
        this._isLoading.next(false);
      }
    }
  }
  /**
   * setBackgroundColor
   */
  public setBackgroundColor(
    value: string,
    overlayElement: HTMLElement = this.overlayElement
  ) {
    overlayElement.style.backgroundColor = value;
  }
  /**
   * setSpinnerSize
   */
  public setSpinnerSize(
    value: string,
    loaderElement: HTMLElement = this.loaderElement
  ) {
    loaderElement.style.width = value;
    loaderElement.style.height = value;
  }

}
