import {
  Component,
  Input,
  OnDestroy,
  inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';

import {
  MatDrawerMode,
  MatSidenavModule,
} from '@angular/material/sidenav';

import { DrawerService } from 'src/app/services/drawer.service';

@Component({
  selector: 'app-drawer',
  standalone: true,
  imports: [CommonModule, MatSidenavModule],
  template: `
    <mat-sidenav-container class="container">
      <mat-sidenav
        [mode]="mode"
        [opened]="open$ | async"
        [fixedInViewport]="fixed"
        [fixedTopGap]="fixedTopGap"
        [fixedBottomGap]="fixedBottomGap"
        position="end"
        disableClose
      >
        <ng-content></ng-content>
      </mat-sidenav>
    </mat-sidenav-container>
  `,
  styleUrls: ['./drawer.component.scss'],
})
export class DrawerComponent implements OnDestroy {
  @Input() backdrop: boolean = false;
  @Input() mode: MatDrawerMode = 'over';
  @Input() fixed: boolean = true;
  @Input() fixedTopGap: number = 0;
  @Input() fixedBottomGap: number = 0;

  private drawerService = inject(DrawerService);

  public open$ = this.drawerService.isOpen;

  ngOnDestroy(): void {
    this.drawerService.cerrarDrawer();
  }
}
