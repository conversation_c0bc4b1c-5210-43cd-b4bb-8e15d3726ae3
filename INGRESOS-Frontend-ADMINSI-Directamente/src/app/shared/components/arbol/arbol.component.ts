import { Component, Input, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';

import { CollapseDirective } from 'ngx-bootstrap/collapse';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';

@Component({
  selector: 'app-arbol',
  standalone: true,
  imports: [CommonModule, CollapseDirective, FontAwesomeModule],
  styleUrls: ['./arbol.component.scss'],
  template: `
    <div class="d-flex flex-column position-relative" style="gap: 6px">
      <div class="d-flex cursor-pointer" style="gap: 4px">
        <fa-icon
          *ngIf="tieneHijos"
          class="icon"
          [icon]="icono"
          (click)="clickArbol()"
        />
        <ng-template
          [ngTemplateOutlet]="template"
          [ngTemplateOutletContext]="{ $implicit: data }"
        />
      </div>
      <div
        *ngIf="tieneHijos"
        class="px-4"
        [collapse]="hijosColapsados"
        [isAnimated]="true"
      >
        <ng-container>
          <app-arbol
            *ngFor="let elemento of data[childrenProp]"
            [data]="elemento"
            [childrenProp]="childrenProp"
            [template]="template"
          />
        </ng-container>
      </div>
    </div>
  `,
})
export class ArbolComponent {
  @Input() childrenProp: string = '';
  @Input() data: { [key: string]: any };
  @Input() template: TemplateRef<typeof this.data>;

  public hijosColapsados: boolean = true;

  public get icono(): IconProp {
    return this.hijosColapsados ? 'chevron-right' : 'chevron-down';
  }

  public get tieneHijos(): boolean {
    return this.data[this.childrenProp] && this.data[this.childrenProp].length;
  }

  public clickArbol() {
    this.hijosColapsados = !this.hijosColapsados;
  }
}
