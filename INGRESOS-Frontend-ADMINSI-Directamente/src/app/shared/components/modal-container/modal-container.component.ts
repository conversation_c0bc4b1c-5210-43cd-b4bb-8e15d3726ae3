import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, Input, Output, inject } from '@angular/core';
import { ModalService } from 'src/app/services/modal.service';

@Component({
  selector: 'app-modal-container',
  template: `
    <header>
      <h5 class="modal-title font-weigth-bold">{{ titulo }}</h5>
      <button
        type="button"
        class="btn btn-outline-dark"
        aria-label="Close"
        (click)="onCerrar()"
      >
        Cerrar
      </button>
    </header>
    <div class="modal-body rounded-0 p-4">
      <ng-content></ng-content>
    </div>
  `,
  standalone: true,
  styleUrls: ['./modal-container.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [CommonModule],
})
export class ModalContainerComponent {
  @Input() titulo: string = '';
  @Input() cerrarEnClick: boolean = false;

  @Output() cerrar = new EventEmitter<void>();

  private modalService = inject(ModalService);

  /**
   * Cierra el modal y emite un evento
   */
  public onCerrar() {
    if (this.cerrar) this.modalService.cerrarModal();
    else this.cerrar.emit();
  }

}
