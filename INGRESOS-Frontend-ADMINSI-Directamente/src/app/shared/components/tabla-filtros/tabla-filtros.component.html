<div class="row">
  <div *ngIf="busqueda" class="col-12">
    <div class="row d-flex justify-content-end">
      <div class="col-auto">
        <div class="input-group input-group-sm mb-1">
          <div class="input-group-prepend">
            <span class="input-group-text">
              <fa-icon icon="magnifying-glass" class="text-secondary" />
            </span>
          </div>
          <input type="text" class="form-control" [(ngModel)]="valorBusqueda" [placeholder]="placeholder"
            (keydown)="cambiarBusqueda($event)" pattern="[a-zA-Z ]*" maxlength="50" appRestringirTipo
            permitir="letrasYNumeros">
        </div>
      </div>
    </div>
  </div>
  <div class="col-12">
    <div class="row d-flex justify-content-end">
      <div *ngIf="fecha" class="col-auto">
        <label for="dropdown" class="text-secondary">Fecha:</label>
        <a class="nav-link dropdown-toggle text-secondary font-weight-bold" data-toggle="dropdown" role="button">
          {{ fechaMostrar }}
        </a>
        <div class="dropdown-menu" (click)="clickPropagacion($event)">
          <mat-calendar class="calendario" [(selected)]="fechaPicker" (selectedChange)="cambiarFecha($event)"
            [maxDate]="maxFecha" [minDate]="minFecha" (_userSelection)="seleccionarFecha($event)" />
        </div>
      </div>
      <div *ngIf="estatus" class="col-auto">
        <div class="btn-group d-flex align-items-end position-relative overflow-visible">
          <div class="dropdown">
            <label for="dropdown" class="text-secondary">Estatus:</label>
            <a class="nav-link dropdown-toggle text-secondary font-weight-bold" data-toggle="dropdown" role="button">
              {{ estatusMostrar }}
            </a>
            <div class="dropdown-menu">
              <a class="dropdown-item" (click)="cambiarEstatus(null)">Todos</a>
              <a class="dropdown-item" *ngFor="let estatus of opcionesEstatus"
                (click)="cambiarEstatus(estatus.valor)">{{estatus.etiqueta}}</a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>