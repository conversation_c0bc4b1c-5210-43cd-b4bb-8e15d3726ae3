import { Component, EventEmitter, inject, Input, OnInit, Output } from '@angular/core';
import { MatCalendarUserEvent, MatDatepickerInputEvent, MatDatepickerModule } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { DateTime } from 'luxon';
import { CommonModule } from '@angular/common';
import { EstatusEnum } from 'src/app/enums/estatus.enum';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { FormsModule } from '@angular/forms';
import { DirectivesModule } from 'src/app/directives/directives.module';
import { DropdownDatePickerComponent } from '../dropdown-date-picker/dropdown-date-picker.component';

@Component({
  selector: 'app-tabla-filtros',
  templateUrl: './tabla-filtros.component.html',
  styleUrls: ['./tabla-filtros.component.scss'],
  standalone: true,
  imports: [MatDatepickerModule, MatInputModule, CommonModule, FontAwesomeModule, FormsModule, DirectivesModule, DropdownDatePickerComponent]
})
export class TablaFiltrosComponent {
  @Input() fecha: boolean = false;
  @Input() estatus: boolean = false;
  @Input() busqueda: boolean = false;

  @Input() valorFecha: DateTime;
  @Input() valorEstatus: EstatusEnum;
  @Input() valorBusqueda: string;

  @Input() formatoFecha: string = 'dd-MM-yyyy';
  @Input() fechaPicker: Date;
  @Input() placeholder: string = 'Buscar...';
  @Input() maxFecha: Date;
  @Input() minFecha: Date;

  @Output() valorFechaChange = new EventEmitter();
  @Output() valorEstatusChange = new EventEmitter();
  @Output() valorBusquedaChange = new EventEmitter();
  @Output() filtroChange = new EventEmitter();

  public cerrarFecha: boolean = false;
  public fechaMostrar: string = 'Todos';
  public estatusMostrar: string = 'Todos';
  public opcionesEstatus = [
    {
      etiqueta: EstatusEnum[EstatusEnum.Pendiente],
      valor: EstatusEnum.Pendiente,
    },
    {
      etiqueta: EstatusEnum[EstatusEnum.Exito],
      valor: EstatusEnum.Exito,
    },
    {
      etiqueta: EstatusEnum[EstatusEnum.Error],
      valor: EstatusEnum.Error,
    }
  ];

  private hoy = DateTime.now();
  private ayer = DateTime.now().minus({ days: 1 });


  /**
   * Función que emite el evento de cambio de fecha de los filtros
   */
  public cambiarFecha(event: Date) {
    this.cerrarFecha = true;
    let fechaSeleccionada = DateTime.fromJSDate(event);
    const mismoDia = (a: DateTime, b: DateTime) =>
      a.toFormat(this.formatoFecha) === b.toFormat(this.formatoFecha);

    if (mismoDia(fechaSeleccionada, this.hoy)) {
      this.fechaMostrar = 'Hoy';
    } else if (mismoDia(fechaSeleccionada, this.ayer)) {
      this.fechaMostrar = 'Ayer';
    } else {
      this.fechaMostrar = fechaSeleccionada.toFormat(this.formatoFecha);
    }

    this.fechaPicker = new Date(fechaSeleccionada.toJSDate());
    this.valorFechaChange.emit(fechaSeleccionada);
    this.filtroChange.emit();
  }

  /**
   * Evento que reacciona al cambio de estatus de los filtros
   */
  public cambiarEstatus(estatus: EstatusEnum): void {
    if (estatus != null) {
      this.estatusMostrar = EstatusEnum[estatus];
      this.valorEstatusChange.emit(estatus);
    } else {
      this.estatusMostrar = 'Todos';
      this.valorEstatusChange.emit(null);
    }
    this.filtroChange.emit();
  }

  /**
   * Evento que reacciona al cambio del input de búsqueda
   */
  public cambiarBusqueda(e: KeyboardEvent): void {
    if (e.key == 'Enter') {
      this.valorBusquedaChange.emit(this.valorBusqueda);
      this.filtroChange.emit();
    }
  }

  /**
   * Evento que reacciona al cambio de fecha
   */
  seleccionarFecha(e: MatCalendarUserEvent<any>): void {
    this.cerrarFecha = true;
    if (this.valorFecha?.equals(DateTime.fromJSDate(e.value))) {
      this.fechaMostrar = 'Todos';
      this.valorFechaChange.emit(null);
      this.filtroChange.emit();
    }
  }

  /**
   * Evento que reacciona al reaizar click en el dropdown y puede detener la propagación
   * del evento predeterminado en caso de ser necesario.
   * @param e Evento
   */
  clickPropagacion(e) {
    if(!this.cerrarFecha)
      e.stopPropagation();

    this.cerrarFecha = false;
  }
}
