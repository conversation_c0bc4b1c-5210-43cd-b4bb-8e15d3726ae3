import {
  AfterViewInit,
  Component,
  OnD<PERSON>roy,
  TemplateRef,
  ViewChild,
  inject,
} from '@angular/core';
import { CommonModule } from '@angular/common';

import { Subject, takeUntil } from 'rxjs';
import { BsModalRef } from 'ngx-bootstrap/modal';

import { ModalService } from 'src/app/services/modal.service';
import { DialogoConfirmacionService } from 'src/app/services/dialogo-confirmacion.service';

@Component({
  selector: 'app-dialogo-confirmacion',
  standalone: true,
  imports: [CommonModule],
  template: `
    <ng-template #dialogo>
      <div class="root">
        ¿Estás seguro?
        <label>Se perderán los cambios realizados</label>
        <div class="btns">
          <button (click)="confirmarDialogo(false)" class="btn btn-primary">
            Cancelar
          </button>
          <button (click)="confirmarDialogo(true)" class="btn btn-success">
            Confirmar
          </button>
        </div>
      </div>
    </ng-template>
  `,
  styles: [
    `
      .root {
        padding: 12px 8px 12px 8px;
        display: flex;
        flex-direction: column;
        gap: 6px;
      }
      .btns {
        display: flex;
        width: 100%;
        gap: 8px;
        justify-content: flex-end;
      }
    `,
  ],
})
export class DialogoConfirmacionComponent implements AfterViewInit, OnDestroy {
  @ViewChild('dialogo', { static: true }) dialogoRef: TemplateRef<{}>;

  private dialogoConfirmacionService = inject(DialogoConfirmacionService);
  private modalService = inject(ModalService);

  public dialogoVisible$ = this.dialogoConfirmacionService.dialogoVisible$;
  private destroy$ = new Subject<void>();

  private modalRef: BsModalRef;

  public confirmarDialogo(confirmado: boolean) {
    this.dialogoConfirmacionService.responderDialogo(confirmado);
  }

  ngAfterViewInit(): void {
    this.dialogoConfirmacionService.dialogoVisible$
      .pipe(takeUntil(this.destroy$))
      .subscribe((visible: boolean) => {
        if (!visible) {
          this.modalService.cerrarModal(this.modalRef);
          return;
        }

        this.modalRef = this.modalService.abrirModal(this.dialogoRef, {
          class: 'modal-dialog-centered',
        });
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
  }
}
