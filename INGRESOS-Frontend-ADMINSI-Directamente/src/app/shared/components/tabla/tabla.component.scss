:host ::ng-deep {
  $gray: #EAF0F6;
  $text-color: #33404E;
  $header-color: #33404E;
  $border-color: #EBEBEB;
  $hover-color: rgba(64, 154, 142, 0.30);
  $border: 2px solid $border-color;
  $header-padding: 0.6rem 3rem;
  $row-padding: 0.4rem 3rem;
  
  .scroll {
    max-height: 280px;
    overflow-y: auto;
  }
  
  .table-responsive {
    position: relative;
    border: 1px solid $border-color;
    border-radius: 6px;
    min-height: 380px;
  }
  
  .scroll-head {
    position: sticky;
    top: 0;
    width: 100%;
  }
  
  .dropdown-menu {
    max-height: 120px;
    overflow-y: auto;
  }
  
  .btn {
    font-size: 1.1em !important;
  }
  
  table {
    border-radius: 6px;
    margin-bottom: 0;
  
    thead {
      th {
        background-color: $gray;
      }

      tr {
        th {
          border-top: none !important;
          vertical-align: middle;
          color: $header-color;
          font-weight: 600 !important;
          font-size: 14px;
          font-weight: normal;
          white-space: nowrap;
          text-transform: uppercase;
          padding: $header-padding;
        }
  
        th:first-child {
          border-top-left-radius: 4px;
        }
  
        th:last-child {
          border-top-left-radius: 4px;
        }
      }
    }
  
    tbody {
      tr {
        td {
          border-top: none !important;
          border-bottom: $gray 1px solid;
          background-color: white;
          vertical-align: middle;
          color: $text-color;
          font-size: 14px;
          max-height: 20px;
          text-overflow: ellipsis;
          white-space: no-wrap;
          padding: $row-padding;
          line-height: 20px;
  
          .cell {
            height: 45px !important;
          }
        }
      }
    }
  }
  
  tr.click {
    cursor: pointer;
    background-color: $hover-color !important;
    
    &:hover {
      border-radius: none;
      cursor: pointer;
      transition: background-color .2s linear; 
      
      td {
        background-color: $hover-color !important;
        transition: background-color .2s linear; 
      }
    }
  }
}
