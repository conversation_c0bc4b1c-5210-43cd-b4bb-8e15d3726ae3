<div 
  class="table-responsive"
  [ngClass]="{ 'scroll': scroll }"
  [ngStyle]="{ 'max-height': scroll ? altura + 'px' : 'unset' }"
>
  <table class="table">
    <thead *ngIf="cabeceros.length" [ngClass]="{ 'scroll-head': scroll }">
      <tr>
        <th *ngFor="let cabecero of cabeceros" scope="col">
          {{ cabecero.label }}
        </th>
        <th *ngIf="detalle" scope="col">Detalle</th>
        <th *ngIf="acciones">Acciones</th>
        <th *ngIf="estatus">Estatus</th>
        <th *ngIf="editar"></th>
        <th *ngIf="descarga"></th>
        <th *ngIf="seleccionar">
          <input *ngIf="check" type="checkbox" (change)="seleccionarTodos($event)"
          [(ngModel)]="checkTodos" [indeterminate]="esIndeterminado">
        </th>
        <th *ngIf="eliminar"></th>
      </tr>
    </thead>
    <tbody *ngIf="cabeceros.length">
      <tr *ngFor="let registro of registros" [ngClass]="{ 'click': click }" (click)="onClick.emit(registro)">
        <td
          *ngFor="let cabecero of cabeceros"
          [ngStyle]="cabecero.estilo ? cabecero.estilo(registro) : {}"
          [ngClass]="cabecero.clases ? cabecero.clases(registro) : {}"
        >
          <ng-container *ngIf="cabecero.transform">
            {{cabecero.transform(registro)}}
          </ng-container>
          <ng-container
            *ngIf="cabecero.template"
            [ngTemplateOutlet]="cabecero.template"
            [ngTemplateOutletContext]="{
              $implicit: registro
            }"
          ></ng-container>
          <ng-container *ngIf="!(cabecero.transform || cabecero.template)">
            {{ registro[cabecero.key] }}
          </ng-container>
        </td>
        <td *ngIf="detalle">
          <button
            class="btn btn-primary text-nowrap"
            (click)="onDetalle.emit(registro)"
          >
            Ver detalle
          </button>
        </td>
        <td *ngIf="acciones">
          <button
            class="btn btn-danger text-nowrap"
            (click)="onRegistrar.emit(registro)"
          >
            Registrar
          </button>
        </td>
        <td *ngIf="estatus">
          <div class="d-flex justify-content-center w-100">
            <app-estatus [estatus]="registro?.opc_activo" />
          </div>
        </td>
        <td *ngIf="editar">
          <img
            (click)="onEditar.emit(registro)"
            style="cursor: pointer"
            src="assets/svg/edit.svg"
          />
        </td>
        <td *ngIf="seleccionar">
          <input *ngIf="check" type="checkbox" (change)="checkboxChange($event, registro)" [checked]="seleccionado(registro)" />
          <input *ngIf="radio" name="seleccion" type="radio" (change)="onSeleccionar.emit(registro)">
        </td>
        <td *ngIf="eliminar">
          <fa-icon [icon]="faTrash" (click)="onEliminar.emit(registro)" size="lg" class="text-dark" style="cursor: pointer"/>
        </td>
      </tr>
      <tr *ngIf="!registros?.length && !(loaderService.isLoading | async) && mensajeVacio && mostrarMensajeVacio">
        <td [attr.colspan]="cantidadColumnas" style="height: 350px">
          <h5 class="text-center">{{mensajeVacio}}</h5>
        </td>
      </tr>
    </tbody>
    <ng-content />
  </table>
</div>
<div class="pt-4">
  <app-paginador
    *ngIf="paginado"
    [total]="total"
    [(registrosPorPagina)]="registrosPorPagina"
    (onPaginaChange)="onPaginaChange.emit($event)"
    (onCantidadChange)="cambiarCantidad($event)"
    [mostrarLimites]="mostrarLimites"
  />
</div>
