import { Injectable, inject } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { BehaviorSubject, Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class ToasterService {

  private messageSubject: BehaviorSubject<any> = new BehaviorSubject(null);
  public readonly $message: Observable<any> = this.messageSubject.asObservable();

  private toastService = inject(ToastrService);

  /**
    * toasterVariants
    */
  public showError(body: string, title?: string) {
    this.toastService.error(body, title);
  }
  public showWarn(body: string, title?: string){
    this.toastService.warning(body, title);
  }
  public showInfo(body: string, title?: string) {
    this.toastService.info(body, title);
  }
  public showApproval(body: string, title?: string) {
    this.toastService.success(body, title);
  }

  public showMesage(body: string, title?: string) {
    this.toastService.show(body, title);
  }

}
