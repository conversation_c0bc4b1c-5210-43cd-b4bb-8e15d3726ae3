
.toaster {
    z-index: -1;
    opacity: 0;
    position: fixed;
    right: 2vw;
    top: 2.2vh;
    width: 318px;
    padding: 1rem;
    border-radius: 16px;
    box-shadow: 0px 6px 24px rgba(25,75,123,0.18);
}
.toaster.show {
    z-index: 9999;
    opacity: 1;
    top: 2vh;
    transition: all 500ms;
}

.toaster header {
    display: flex;
    justify-content: space-between;
    text-transform: uppercase;
    margin-bottom: 1rem;
}
:host ::ng-deep {
    .toaster a {
        margin-top: 1rem;
        display: block;
    }
}
.toaster header .close {
    color: #0076A9;
    cursor: pointer;
}
.toaster header .tag::before {
    width: 25px;
    height: 25px;
    padding: 1px;
    display: inline-block;
    text-align: center;
    border-radius: 50%;
    border: 2px solid;
    margin-right: 4px;
    opacity: 0.7;
}

// VARIANTS

.error {
    background-color: #FFF0EF;
    color: #BA4B44;
    .tag::before {
        content: '❌';
        border-color: #BA4B44;
    }
}
.warn {
    background-color: #FFF4E8;
    color: #AB5C00;
    .tag::before {
        content: '❕';
        border-color: #AB5C00;
    }
}
.info {
    background-color: #E4F7FF;
    color: #0076A9;
    .tag::before {
        content: 'ℹ️';
        border-color: #0076A9;
    }
}
.approval {
    background-color: #E5FFEB;
    color: #2E8241;
    .tag::before {
        content: '✔️';
        border-color: #2E8241;
    }
}
.message {
    background-color: #FFFFFF;
    color: #1B1A16;
    .tag::before {
        content: '💬';
        border-color: #bdbdbd !important;
    }
}