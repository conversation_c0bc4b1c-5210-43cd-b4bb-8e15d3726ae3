import { HttpResponse } from "@angular/common/http";

/**
 * Función que descarga un 
 * archivo proveniente de un buffer
 */
const descargarArchivo = (
  resp: HttpResponse<Blob>
) => {
  const blob = new Blob([resp.body]);
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');  
  a.href = url;
  
  let nombre = ''
  if (resp.headers.get('content-disposition')) {
    const [_, adjunto] = resp.headers.get('content-disposition').split(';');
    nombre = adjunto.split('=')[1];
  }

  a.download = nombre;
  document.body.appendChild(a);
  a.click();

  document.body.removeChild(a);
  window.URL.revokeObjectURL(url);
};

export default descargarArchivo;
