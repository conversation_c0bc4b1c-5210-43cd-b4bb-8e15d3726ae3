import { DateTime } from "luxon";

/**
 * Toma una cadena de texto que representa una fecha y regresa la fecha con el mes capitalizado
 * @param {string} fecha - La fecha a convertir
 * @returns La fecha formateada.
 */
const formatearFecha = (fecha: string) => {
    let cadenaFecha = DateTime.fromISO(fecha).toFormat('dd-LLL-yyyy');
    cadenaFecha = cadenaFecha.replace(cadenaFecha[3], cadenaFecha[3].toUpperCase());
    return cadenaFecha;
}

export default formatearFecha;