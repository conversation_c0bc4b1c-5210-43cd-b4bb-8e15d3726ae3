import { Injectable } from '@angular/core';
import { HttpRe<PERSON>, Http<PERSON><PERSON>ler, HttpInterceptor, HttpResponse, HttpParams } from '@angular/common/http';
import { Observable, catchError, finalize, map, throwError } from 'rxjs';

import { environment } from 'src/environments/environment';
import { SecureStorageService } from '../services/secure-storage.service';
import { LoaderService } from '../shared/loader/services/loader.service';
import { ToasterService } from '../shared/toaster/services/toaster.service';
import { SessionService } from '../state/session.service';
import { ERROR_GENERICO, RECURSO_NO_ENCONTRADO } from '../constants/mensajes';
import esNuloOIndefinido from '../utils/esNuloOIndefinido';

@Injectable()
export class JwtInterceptor implements HttpInterceptor {
  constructor(
    private storageService: SecureStorageService,
    private loaderService: LoaderService,
    private toasterService: ToasterService,
    private sessionService: SessionService,
  ) {}

  private setTokenHeaders(request: HttpRequest<any>) {
    // Validación adicional de seguridad para dominios confiables
    const allowedDomains = ['localhost', 'coppel.io', 'coppel.com'];
    const url = new URL(request.url);
    const isAllowedDomain = allowedDomains.some(domain =>
      url.hostname === domain || url.hostname.endsWith('.' + domain)
    );

    if (!isAllowedDomain) {
      console.warn('Dominio no autorizado para envío de tokens:', url.hostname);
      return request; // No agrega headers para dominios no confiables
    }

    const token = this.storageService.getItem('token');
    const idp = this.storageService.getItem('idp');

    if (!token || !idp) {
      console.warn('Token o IDP ausentes. Intercepción cancelada.');
      this.sessionService.logout();
      return request; // No agrega headers si no hay token o idp
    }

    return request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`,
        Idp: idp
      }
    });
  }

  private removerNulos(request: HttpRequest<any>) {
    let params: HttpParams = new HttpParams();
    request.params.keys().forEach((key) => {
      const value = request.params.get(key);
      if (!esNuloOIndefinido(value)) {
        params = params.set(key, value);
      }
    });
    return request.clone({ params });
  }

  private personalizarAlertas(request: HttpRequest<any>) {
    const noSpinner = request.params.get('noSpinner');
    const noAlertaError = request.params.get('noAlertaError');

    let params = request.params;
    params = params.set('noSpinner', null);
    params = params.set('noAlertaError', null);

    return [request.clone({ params }), noSpinner, noAlertaError];
  }

  public intercept(request: HttpRequest<any>, next: HttpHandler): Observable<any> {
    // Validación estricta: solo URLs que comiencen exactamente con las configuradas
    let requireAuth = environment.interceptorApproach.some((source) =>
      request.url.startsWith(source)
    );

    if (requireAuth) {
      request = this.setTokenHeaders(request);
    }

    let [requestClone, noSpinner, noAlertaError] =
      this.personalizarAlertas(request);

    request = requestClone as HttpRequest<any>;
    request = this.removerNulos(request);

    if (!noSpinner) this.loaderService.setVisibleState(true);

    return next.handle(request).pipe(
      map((event) => {
        if (event instanceof HttpResponse) {
          return event.clone({ body: event.body?.data });
        }
        return event;
      }),
      finalize(() => this.loaderService.setVisibleState(false)),
      catchError((error) => {
        if (!noAlertaError && String(error.status).startsWith('5')) {
          this.toasterService.showError(error.error || ERROR_GENERICO, 'Error');
        } else if (!noAlertaError && String(error.status).startsWith('4')) {
          this.toasterService.showWarn(
            typeof error.error === 'string'
              ? error.error
              : RECURSO_NO_ENCONTRADO
          );
        }

        if (error.status === 401) {
          this.sessionService.logout();
        }

        return throwError(() => error);
      })
    );
  }
}
