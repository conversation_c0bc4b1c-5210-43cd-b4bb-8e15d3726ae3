## Comadreja [**1.3.0**] <sub>2023-05-17</sub>

### Added
    Lógica de consumo de sesión en SSO (Authentication) "/me" del lado del cliente
    Nuevo script en package.json "test:prod" para simular environments de producción
    Anexo a .gitignore archivos autogenerados de .vscode
    Anexo a angular.json especificación en cli '"packageManager":"npm"'
### Changed
    Actualización de core de Angular 14 -> 15
    Actualización de ECMAScript base ES2020 -> ES2022
    Cambio de base href en index.html "./" -> "/" para evitar missmatch en routing
    Reorganización en componentes de módulo hacia folder "components" en apego al estándar de Scaffolding (https://sites.google.com/coppel.com/developers/frameworks/webclient/scaffolding)
    Renombrado de script en package.json "start-shared" -> "start:shared"
    Renombrado de script en package.json "json-start" -> "fake-api-start"
    Patch de compatibilidad de configuración en arquitectura
    Actualización de dependencias con compatibilidad para Angular 15
        "dependencies": {
            "ngx-bootstrap": "^10.0.0",
            "rxjs": "~7.8.0",
            "tslib": "^2.3.0",
            "zone.js": "~0.12.0"
        },
        "devDependencies": {
            "concurrently": "^8.0.0", https://github.com/open-cli-tools/concurrently/compare/v7.6.0...v8.0.0
            "typescript": "~4.9.4" https://devblogs.microsoft.com/typescript/announcing-typescript-4-9/
        },
### Fixed
    Corrección en "full-layout.component.ts" para llamada a notificaciones
    Corrección en error "unmatched pseudo-class :lang" Angular 12 + Bootstrap 4
    Corrección en error de captura de nonce en secure-storage.service.ts
### Removed
    Eliminación de integración con jest corrupta e incompatible con Angular 15
    Eliminación de dependencia "path" sin utilidad en la plantilla 
    Eliminación de polyfills.ts
    Eliminación de filter pipes en base al estándar (https://angular.io/guide/styleguide#do-not-add-filtering-and-sorting-logic-to-pipes)
    Eliminación de funcionalidades search de layout poco usuales
    Eliminación de script package.json "dns" redundante a "start:shared"
    Eliminación de script package.json "dns-json" redundante a "dev:shared"
    Eliminación de script package.json "json-start-shared" debido a que no se requiere para productivo