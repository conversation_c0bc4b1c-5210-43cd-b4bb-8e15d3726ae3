{"name": "front-sys-ingresos", "version": "1.0.0", "description": "Aplicación Front-End (Angular) para el control de accesos al SI", "coppel": {"name": "@coppelframework/webclient-angularspa", "version": "1.3.0", "homepage": "https://gitlab.coppel.io/oc/coppelframework-webclient-angularspa.git", "docs": "https://sites.google.com/coppel.com/developers/frameworks/webclient"}, "author": "GAMAN SOLUTIONS | Ing. Yahred Gastelum", "scripts": {"ng": "ng", "build": "ng build --configuration production --aot --base-href /admin-si/", "watch": "ng build --watch --configuration development", "start": "ng serve", "start:prod": "ng serve --configuration production", "start:shared": "ng serve --host 0.0.0.0 --disable-host-check", "dev": "concurrently \"npm run start\" \"npm run fake-api-start\"", "dev:shared": "concurrently \"npm run start:shared\" \"npm run fake-api-start\"", "fake-api-start": "cd fake-api && npm run json-start"}, "private": true, "dependencies": {"@angular/animations": "^18.2.13", "@angular/cdk": "^18.2.14", "@angular/common": "^18.2.13", "@angular/compiler": "^18.2.13", "@angular/core": "^18.2.13", "@angular/forms": "^18.2.13", "@angular/localize": "^18.2.13", "@angular/material": "^18.2.14", "@angular/material-moment-adapter": "^18.2.14", "@angular/platform-browser": "^18.2.13", "@angular/platform-browser-dynamic": "^18.2.13", "@angular/router": "^18.2.13", "@azure/msal-angular": "^4.0.6", "@azure/msal-browser": "^4.7.0", "@babel/runtime": "^7.23.1", "@coreui/icons": "^2.1.0", "@fortawesome/angular-fontawesome": "^0.15.0", "@fortawesome/fontawesome-svg-core": "^6.2.1", "@fortawesome/free-brands-svg-icons": "^6.2.1", "@fortawesome/free-regular-svg-icons": "^6.2.1", "@fortawesome/free-solid-svg-icons": "^6.2.1", "@ng-bootstrap/ng-bootstrap": "^17.0.1", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.2", "bs-stepper": "^1.7.0", "font-awesome": "^4.7.0", "js2xmlparser": "^5.0.0", "libsodium-wrappers": "^0.7.15", "luxon": "^3.2.1", "moment-timezone": "^0.5.43", "ngx-bootstrap": "^18.1.3", "ngx-toastr": "^16.2.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular/build": "^18.2.15", "@angular/cli": "~18.2.15", "@angular/compiler-cli": "^18.2.13", "@types/libsodium-wrappers": "^0.7.14", "@types/luxon": "^3.3.2", "concurrently": "^8.0.0", "csstype": "^3.1.2", "typescript": "~5.4.5"}, "browser": {"crypto": false}, "overrides": {"esbuild": "0.25.4", "@angular/build": {"esbuild": "0.25.4"}, "vite": {"esbuild": "0.25.4"}}}